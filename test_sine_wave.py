#!/usr/bin/env python3
"""
Simple Sine Wave Test Application
This script tests if the sine wave generator can run properly
"""

import sys
import os

print("=" * 50)
print("SINE WAVE APPLICATION DIAGNOSTIC TEST")
print("=" * 50)

# Test 1: Check Python version
print(f"Python version: {sys.version}")

# Test 2: Check imports
print("\nTesting imports...")
try:
    import numpy as np
    print("✓ NumPy imported successfully")
    print(f"  NumPy version: {np.__version__}")
except ImportError as e:
    print(f"✗ NumPy import failed: {e}")
    sys.exit(1)

try:
    import matplotlib
    matplotlib.use('TkAgg')  # Set backend before importing pyplot
    import matplotlib.pyplot as plt
    print("✓ Matplotlib imported successfully")
    print(f"  Matplotlib version: {matplotlib.__version__}")
    print(f"  Backend: {matplotlib.get_backend()}")
except ImportError as e:
    print(f"✗ Matplotlib import failed: {e}")
    sys.exit(1)

try:
    from matplotlib.animation import FuncAnimation
    from matplotlib.widgets import Slider, Button
    print("✓ Matplotlib widgets imported successfully")
except ImportError as e:
    print(f"✗ Matplotlib widgets import failed: {e}")
    sys.exit(1)

# Test 3: Create a simple sine wave
print("\nTesting sine wave generation...")
try:
    x = np.linspace(0, 2*np.pi, 100)
    y = np.sin(x)
    print("✓ Sine wave data generated successfully")
    print(f"  Data points: {len(x)}")
    print(f"  Min value: {np.min(y):.3f}")
    print(f"  Max value: {np.max(y):.3f}")
except Exception as e:
    print(f"✗ Sine wave generation failed: {e}")
    sys.exit(1)

# Test 4: Test GUI creation
print("\nTesting GUI creation...")
try:
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.plot(x, y, 'b-', linewidth=2)
    ax.set_title('Test Sine Wave')
    ax.set_xlabel('x')
    ax.set_ylabel('sin(x)')
    ax.grid(True)
    print("✓ Basic plot created successfully")
    
    # Test interactive elements
    ax_slider = plt.axes([0.2, 0.02, 0.6, 0.03])
    slider = Slider(ax_slider, 'Frequency', 0.1, 5.0, valinit=1.0)
    print("✓ Slider widget created successfully")
    
    # Show the plot
    print("\n" + "=" * 50)
    print("LAUNCHING TEST APPLICATION")
    print("=" * 50)
    print("If you see a window with a sine wave and a slider,")
    print("the application is working correctly!")
    print("Close the window to continue...")
    
    plt.tight_layout()
    plt.show()
    
    print("✓ GUI test completed successfully")
    
except Exception as e:
    print(f"✗ GUI creation failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n" + "=" * 50)
print("ALL TESTS PASSED!")
print("The sine wave application should work correctly.")
print("=" * 50)
