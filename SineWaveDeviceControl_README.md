# Sine Wave Device Control UI

This is a Visual Studio Windows Forms application that provides a user interface for controlling sine wave generation across multiple devices.

## Features

- Tab-based interface with 4 device tabs
- Device number selection in each tab
- 4 control buttons: Start, Stop, Reset, and Settings

## Project Structure

The solution contains the following key files:

- `DeviceControlUI.cs` - Main form code
- `DeviceControlUI.Designer.cs` - UI layout and design
- `Program.cs` - Application entry point

## How to Open the Project

1. Open Visual Studio
2. Select "Open a project or solution"
3. Navigate to the folder containing the solution file (`SineWaveDeviceControl.sln`)
4. Click "Open"

## How to Run the Application

1. In Visual Studio, press F5 or click the "Start" button in the toolbar
2. The application will compile and run

## UI Description

The UI consists of:

1. **Tab Control** - Contains 4 tabs, one for each device
   - Each tab has a device number selection dropdown

2. **Control Buttons**
   - **Start** - Starts the sine wave generation
   - **Stop** - Stops the sine wave generation
   - **Reset** - Resets all parameters
   - **Settings** - Opens settings dialog

## Customization

You can customize this UI by:

1. Adding more controls to each tab (e.g., frequency, amplitude, phase controls)
2. Implementing the button click handlers in `DeviceControlUI.cs`
3. Adding additional tabs if more devices are needed

## Requirements

- Visual Studio 2017 or later
- .NET Framework 4.7.2 or later
