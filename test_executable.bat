@echo off
echo Testing SineWaveGenerator.exe...
echo.

if exist "dist\SineWaveGenerator.exe" (
    echo ✓ Executable found: dist\SineWaveGenerator.exe
    echo File size:
    dir "dist\SineWaveGenerator.exe" | find "SineWaveGenerator.exe"
    echo.
    echo Attempting to run the executable...
    echo If a window opens, the executable is working correctly.
    echo.
    start "" "dist\SineWaveGenerator.exe"
    echo.
    echo Executable launched. Check for the application window.
) else (
    echo ✗ Executable not found: dist\SineWaveGenerator.exe
    echo The executable may not have been built successfully.
)

echo.
pause
