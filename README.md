# Sine Wave Generator

A Python utility for generating, visualizing, and saving sine waves.

## Features

- Generate sine waves with customizable parameters:
  - Frequency
  - Amplitude
  - Phase
  - Duration
- Visualize waves using matplotlib
- Save waves as WAV audio files
- Generate chords (combinations of sine waves)

## Requirements

- Python 3.6+
- NumPy
- Matplotlib
- SciPy

## Installation

1. Clone this repository or download the files
2. Install the required dependencies:

```bash
pip install numpy matplotlib scipy
```

## Usage

### Basic Example

```python
from sine_wave_generator import SineWaveGenerator

# Create a sine wave generator
generator = SineWaveGenerator()

# Generate a 440 Hz sine wave (A4 note)
time, wave = generator.generate_sine_wave(
    frequency=440,
    amplitude=0.8,
    duration=2.0
)

# Visualize the wave
generator.plot_wave(time, wave, title="440 Hz Sine Wave (A4 note)")

# Save as WAV file
generator.save_as_wav(wave, "A4_note.wav")
```

### Generating a Chord

```python
# Generate a C major chord (C4, E4, G4)
c_major_frequencies = [261.63, 329.63, 392.00]
amplitudes = [0.6, 0.5, 0.5]  # Slightly emphasize the root note

time, chord = generator.generate_chord(
    frequencies=c_major_frequencies,
    amplitudes=amplitudes,
    duration=3.0
)

generator.plot_wave(time, chord, title="C Major Chord")
generator.save_as_wav(chord, "C_major_chord.wav")
```

## API Reference

### SineWaveGenerator Class

#### `__init__(sampling_rate=44100)`
Initialize the sine wave generator with the specified sampling rate.

#### `generate_sine_wave(frequency, amplitude=1.0, phase=0.0, duration=1.0)`
Generate a sine wave with the specified parameters.

#### `plot_wave(time_array, wave_array, title="Sine Wave")`
Plot the generated wave using matplotlib.

#### `save_as_wav(wave_array, filename="sine_wave.wav")`
Save the wave as a WAV audio file.

#### `generate_chord(frequencies, amplitudes=None, phases=None, duration=1.0)`
Generate a chord (sum of multiple sine waves).

## License

This project is open source and available under the MIT License.
