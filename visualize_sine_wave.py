import numpy as np
import matplotlib.pyplot as plt

def visualize_sine_wave(frequency=440, amplitude=1.0, phase=0.0, duration=0.05, sampling_rate=44100):
    """
    Generate and visualize a sine wave with the specified parameters.
    
    Parameters:
    -----------
    frequency : float
        The frequency of the sine wave in Hz
    amplitude : float
        The peak amplitude of the sine wave
    phase : float
        The phase offset in radians
    duration : float
        The duration of the sine wave in seconds
    sampling_rate : int
        The number of samples per second (Hz)
    """
    # Create time array
    num_samples = int(sampling_rate * duration)
    time_array = np.linspace(0, duration, num_samples, endpoint=False)
    
    # Generate sine wave
    sine_wave = amplitude * np.sin(2 * np.pi * frequency * time_array + phase)
    
    # Create a figure with two subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
    fig.suptitle(f'Sine Wave: {frequency} Hz', fontsize=16)
    
    # Plot the sine wave in the first subplot
    ax1.plot(time_array, sine_wave, 'b-')
    ax1.set_title('Sine Wave - Time Domain')
    ax1.set_xlabel('Time (seconds)')
    ax1.set_ylabel('Amplitude')
    ax1.grid(True)
    
    # Add markers for key points
    # Mark the peaks and troughs
    peak_indices = np.where((sine_wave[1:-1] > sine_wave[:-2]) & 
                           (sine_wave[1:-1] > sine_wave[2:]))[0] + 1
    trough_indices = np.where((sine_wave[1:-1] < sine_wave[:-2]) & 
                             (sine_wave[1:-1] < sine_wave[2:]))[0] + 1
    
    if len(peak_indices) > 0:
        ax1.plot(time_array[peak_indices], sine_wave[peak_indices], 'ro', label='Peaks')
    if len(trough_indices) > 0:
        ax1.plot(time_array[trough_indices], sine_wave[trough_indices], 'go', label='Troughs')
    
    # Mark zero crossings
    zero_crossings = np.where(np.diff(np.signbit(sine_wave)))[0]
    if len(zero_crossings) > 0:
        ax1.plot(time_array[zero_crossings], np.zeros(len(zero_crossings)), 'ko', label='Zero Crossings')
    
    ax1.legend()
    
    # Create a circular representation in the second subplot
    theta = np.linspace(0, 2*np.pi, 1000)
    circle_x = np.cos(theta)
    circle_y = np.sin(theta)
    
    # Plot the unit circle
    ax2.plot(circle_x, circle_y, 'k--', alpha=0.3)
    ax2.set_title('Sine Wave - Circular Representation')
    ax2.set_aspect('equal')
    ax2.grid(True)
    
    # Plot a point moving around the circle
    num_points = min(20, len(time_array))
    indices = np.linspace(0, len(time_array)-1, num_points, dtype=int)
    
    for i in indices:
        t = time_array[i]
        angle = 2 * np.pi * frequency * t + phase
        x = np.cos(angle)
        y = np.sin(angle)
        
        # Plot the point on the circle
        ax2.plot(x, y, 'ro', alpha=0.7)
        
        # Draw a line from the center to the point
        ax2.plot([0, x], [0, y], 'r-', alpha=0.3)
        
        # Show the sine value
        ax2.plot([x, x], [0, y], 'b-', alpha=0.5)
    
    # Add labels and arrows
    ax2.annotate('cos(ωt)', xy=(0.5, 0), xytext=(0.7, -0.3), 
                arrowprops=dict(arrowstyle='->'))
    ax2.annotate('sin(ωt)', xy=(0, 0.5), xytext=(-0.3, 0.7), 
                arrowprops=dict(arrowstyle='->'))
    
    ax2.set_xlim(-1.5, 1.5)
    ax2.set_ylim(-1.5, 1.5)
    ax2.set_xlabel('cos(ωt)')
    ax2.set_ylabel('sin(ωt)')
    
    # Add annotations
    wavelength = sampling_rate / frequency
    period = 1.0 / frequency
    
    fig.text(0.1, 0.01, f'Frequency: {frequency} Hz\nPeriod: {period:.6f} seconds\nWavelength: {wavelength:.2f} samples', 
             fontsize=10, bbox=dict(facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9, bottom=0.1)
    plt.show()

if __name__ == "__main__":
    # Visualize a 10 Hz sine wave for better visualization of the wave shape
    visualize_sine_wave(frequency=10, amplitude=1.0, phase=0.0, duration=0.5)
    
    # Uncomment to visualize different frequencies
    # visualize_sine_wave(frequency=440, amplitude=1.0, phase=0.0, duration=0.05)  # A4 note
    # visualize_sine_wave(frequency=261.63, amplitude=1.0, phase=0.0, duration=0.05)  # C4 note
