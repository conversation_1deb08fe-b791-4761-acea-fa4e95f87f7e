#!/usr/bin/env python3
"""
Clean Sine Wave Generator GUI
A simple, reliable sine wave generator with interactive controls
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from matplotlib.widgets import <PERSON><PERSON><PERSON>, Button, RadioButtons
import matplotlib
matplotlib.use('TkAgg')

class SineWaveGUI:
    def __init__(self):
        """Initialize the sine wave generator GUI"""
        print("🌊 Initializing Sine Wave Generator...")
        
        # Parameters
        self.frequency = 1.0
        self.amplitude = 1.0
        self.phase = 0.0
        self.wave_type = 'sine'
        self.is_running = True
        self.animation_speed = 0.1
        
        # Create the main figure
        self.fig = plt.figure(figsize=(12, 8))
        self.fig.suptitle('🌊 Sine Wave Generator', fontsize=16, fontweight='bold')
        
        # Main plot area
        self.ax_main = plt.axes([0.1, 0.4, 0.8, 0.5])
        self.setup_main_plot()
        
        # Create controls
        self.create_controls()
        
        # Animation
        self.time_offset = 0
        self.setup_animation()
        
        print("✅ Sine Wave Generator initialized successfully!")
    
    def setup_main_plot(self):
        """Set up the main plotting area"""
        self.ax_main.set_xlim(0, 4*np.pi)
        self.ax_main.set_ylim(-2.5, 2.5)
        self.ax_main.set_xlabel('Time (radians)', fontsize=12)
        self.ax_main.set_ylabel('Amplitude', fontsize=12)
        self.ax_main.grid(True, alpha=0.3)
        
        # Create x data
        self.x = np.linspace(0, 4*np.pi, 1000)
        
        # Create the line object
        self.line, = self.ax_main.plot([], [], linewidth=3, color='blue')
        
        # Add equation text
        self.equation_text = self.ax_main.text(0.02, 0.95, '', transform=self.ax_main.transAxes,
                                             fontsize=12, verticalalignment='top',
                                             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    def create_controls(self):
        """Create all control widgets"""
        # Frequency slider
        ax_freq = plt.axes([0.15, 0.30, 0.5, 0.03])
        self.freq_slider = Slider(ax_freq, 'Frequency (Hz)', 0.1, 5.0, valinit=1.0, valfmt='%.1f')
        self.freq_slider.on_changed(self.update_frequency)
        
        # Amplitude slider
        ax_amp = plt.axes([0.15, 0.25, 0.5, 0.03])
        self.amp_slider = Slider(ax_amp, 'Amplitude', 0.1, 2.0, valinit=1.0, valfmt='%.1f')
        self.amp_slider.on_changed(self.update_amplitude)
        
        # Phase slider
        ax_phase = plt.axes([0.15, 0.20, 0.5, 0.03])
        self.phase_slider = Slider(ax_phase, 'Phase (rad)', 0, 2*np.pi, valinit=0.0, valfmt='%.2f')
        self.phase_slider.on_changed(self.update_phase)
        
        # Wave type radio buttons
        ax_radio = plt.axes([0.75, 0.15, 0.15, 0.2])
        self.radio = RadioButtons(ax_radio, ('sine', 'cosine', 'square', 'sawtooth'))
        self.radio.on_clicked(self.update_wave_type)
        
        # Control buttons
        ax_pause = plt.axes([0.15, 0.10, 0.1, 0.04])
        self.pause_btn = Button(ax_pause, 'Pause')
        self.pause_btn.on_clicked(self.toggle_pause)
        
        ax_reset = plt.axes([0.3, 0.10, 0.1, 0.04])
        self.reset_btn = Button(ax_reset, 'Reset')
        self.reset_btn.on_clicked(self.reset_parameters)
        
        # Speed slider
        ax_speed = plt.axes([0.15, 0.05, 0.5, 0.03])
        self.speed_slider = Slider(ax_speed, 'Animation Speed', 0.01, 0.5, valinit=0.1, valfmt='%.2f')
        self.speed_slider.on_changed(self.update_speed)
    
    def calculate_wave(self, x, wave_type):
        """Calculate wave values based on type"""
        arg = self.frequency * x + self.phase + self.time_offset
        
        if wave_type == 'sine':
            return self.amplitude * np.sin(arg)
        elif wave_type == 'cosine':
            return self.amplitude * np.cos(arg)
        elif wave_type == 'square':
            return self.amplitude * np.sign(np.sin(arg))
        elif wave_type == 'sawtooth':
            return self.amplitude * (2 * (arg / (2*np.pi) - np.floor(arg / (2*np.pi) + 0.5)))
        else:
            return self.amplitude * np.sin(arg)
    
    def update_frequency(self, val):
        """Update frequency parameter"""
        self.frequency = val
    
    def update_amplitude(self, val):
        """Update amplitude parameter"""
        self.amplitude = val
        self.ax_main.set_ylim(-val*1.2, val*1.2)
    
    def update_phase(self, val):
        """Update phase parameter"""
        self.phase = val
    
    def update_wave_type(self, label):
        """Update wave type"""
        self.wave_type = label
    
    def update_speed(self, val):
        """Update animation speed"""
        self.animation_speed = val
    
    def toggle_pause(self, event):
        """Toggle animation pause"""
        self.is_running = not self.is_running
        self.pause_btn.label.set_text('Resume' if not self.is_running else 'Pause')
    
    def reset_parameters(self, event):
        """Reset all parameters to defaults"""
        self.freq_slider.reset()
        self.amp_slider.reset()
        self.phase_slider.reset()
        self.speed_slider.reset()
        self.frequency = 1.0
        self.amplitude = 1.0
        self.phase = 0.0
        self.animation_speed = 0.1
        self.wave_type = 'sine'
        self.radio.set_active(0)
        self.ax_main.set_ylim(-2.5, 2.5)
    
    def animate(self, frame):
        """Animation function"""
        if self.is_running:
            self.time_offset += self.animation_speed
        
        # Calculate wave
        y = self.calculate_wave(self.x, self.wave_type)
        
        # Update line
        self.line.set_data(self.x, y)
        
        # Update equation text
        equation = f'{self.wave_type.capitalize()} Wave: '
        equation += f'A={self.amplitude:.1f} × {self.wave_type}({self.frequency:.1f}t + {self.phase:.2f})'
        self.equation_text.set_text(equation)
        
        # Update title
        status = "Running" if self.is_running else "Paused"
        self.ax_main.set_title(f'{self.wave_type.capitalize()} Wave - {status}', fontsize=14)
        
        return self.line, self.equation_text
    
    def setup_animation(self):
        """Set up the animation"""
        self.ani = FuncAnimation(self.fig, self.animate, interval=50, blit=True, repeat=True)
    
    def show(self):
        """Display the GUI"""
        print("🚀 Launching Sine Wave Generator GUI...")
        print("📋 Controls:")
        print("   • Use sliders to adjust frequency, amplitude, phase, and speed")
        print("   • Select different wave types with radio buttons")
        print("   • Click 'Pause' to stop/start animation")
        print("   • Click 'Reset' to restore default values")
        print("   • Close the window to exit")
        
        plt.tight_layout()
        plt.show()
        
        print("✅ Sine Wave Generator closed successfully!")

def main():
    """Main function"""
    print("=" * 60)
    print("🌊 SINE WAVE GENERATOR GUI")
    print("=" * 60)
    
    try:
        # Create and show the GUI
        gui = SineWaveGUI()
        gui.show()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
