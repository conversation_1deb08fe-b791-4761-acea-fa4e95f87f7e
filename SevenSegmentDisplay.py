import tkinter as tk
from tkinter import ttk
import time
import threading

class SevenSegmentDisplay:
    """
    A 7-segment display implementation using Tkinter
    """
    
    def __init__(self, master, width=100, height=150, color='red', bg_color='black'):
        self.master = master
        self.width = width
        self.height = height
        self.color = color
        self.bg_color = bg_color
        
        # Create canvas for drawing
        self.canvas = tk.Canvas(master, width=width, height=height, bg=bg_color)
        self.canvas.pack(padx=5, pady=5)
        
        # Define segment coordinates (relative to canvas size)
        self.segments = {
            'a': [(0.1, 0.05), (0.9, 0.05), (0.85, 0.15), (0.15, 0.15)],  # top
            'b': [(0.85, 0.15), (0.9, 0.05), (0.95, 0.45), (0.85, 0.5)],   # top right
            'c': [(0.85, 0.5), (0.95, 0.55), (0.9, 0.95), (0.85, 0.85)],   # bottom right
            'd': [(0.15, 0.85), (0.85, 0.85), (0.9, 0.95), (0.1, 0.95)],   # bottom
            'e': [(0.05, 0.55), (0.15, 0.5), (0.15, 0.85), (0.1, 0.95)],   # bottom left
            'f': [(0.05, 0.05), (0.15, 0.15), (0.15, 0.5), (0.05, 0.45)],  # top left
            'g': [(0.15, 0.45), (0.85, 0.45), (0.85, 0.55), (0.15, 0.55)]  # middle
        }
        
        # Define which segments are active for each digit
        self.digit_segments = {
            0: ['a', 'b', 'c', 'd', 'e', 'f'],
            1: ['b', 'c'],
            2: ['a', 'b', 'g', 'e', 'd'],
            3: ['a', 'b', 'g', 'c', 'd'],
            4: ['f', 'g', 'b', 'c'],
            5: ['a', 'f', 'g', 'c', 'd'],
            6: ['a', 'f', 'g', 'e', 'd', 'c'],
            7: ['a', 'b', 'c'],
            8: ['a', 'b', 'c', 'd', 'e', 'f', 'g'],
            9: ['a', 'b', 'c', 'd', 'f', 'g'],
            'A': ['a', 'b', 'c', 'e', 'f', 'g'],
            'B': ['c', 'd', 'e', 'f', 'g'],
            'C': ['a', 'd', 'e', 'f'],
            'D': ['b', 'c', 'd', 'e', 'g'],
            'E': ['a', 'd', 'e', 'f', 'g'],
            'F': ['a', 'e', 'f', 'g'],
            '-': ['g'],
            ' ': []
        }
        
        self.current_digit = 0
        self.draw_digit(self.current_digit)
    
    def draw_segment(self, segment_name, active=True):
        """Draw a single segment"""
        coords = self.segments[segment_name]
        # Convert relative coordinates to absolute
        abs_coords = []
        for x, y in coords:
            abs_coords.extend([x * self.width, y * self.height])
        
        color = self.color if active else self.bg_color
        outline_color = '#333333' if not active else self.color
        
        self.canvas.create_polygon(abs_coords, fill=color, outline=outline_color, width=1)
    
    def draw_digit(self, digit):
        """Draw a complete digit"""
        self.canvas.delete("all")
        self.current_digit = digit
        
        if digit in self.digit_segments:
            active_segments = self.digit_segments[digit]
            
            # Draw all segments
            for segment in self.segments:
                self.draw_segment(segment, segment in active_segments)
    
    def set_digit(self, digit):
        """Set the display to show a specific digit"""
        self.draw_digit(digit)

class MultiDigitDisplay:
    """
    Multiple 7-segment displays for showing numbers
    """
    
    def __init__(self, master, num_digits=4, width=80, height=120):
        self.master = master
        self.num_digits = num_digits
        self.displays = []
        
        # Create frame for displays
        self.frame = tk.Frame(master)
        self.frame.pack(padx=10, pady=10)
        
        # Create individual displays
        for i in range(num_digits):
            display_frame = tk.Frame(self.frame)
            display_frame.pack(side=tk.LEFT, padx=2)
            display = SevenSegmentDisplay(display_frame, width, height)
            self.displays.append(display)
    
    def set_number(self, number):
        """Set the display to show a number"""
        # Convert number to string and pad with spaces
        num_str = str(number).rjust(self.num_digits)
        
        for i, digit_char in enumerate(num_str):
            if digit_char.isdigit():
                self.displays[i].set_digit(int(digit_char))
            elif digit_char in ['-', ' ']:
                self.displays[i].set_digit(digit_char)
            else:
                self.displays[i].set_digit(' ')
    
    def set_text(self, text):
        """Set the display to show text (limited to available characters)"""
        text = text.upper().rjust(self.num_digits)
        
        for i, char in enumerate(text):
            if i < len(self.displays):
                if char in self.displays[i].digit_segments:
                    self.displays[i].set_digit(char)
                else:
                    self.displays[i].set_digit(' ')

class SevenSegmentApp:
    """
    Main application with controls for the 7-segment display
    """
    
    def __init__(self, root):
        self.root = root
        self.root.title("7-Segment Display Simulator")
        self.root.geometry("600x400")
        
        # Create main frame
        main_frame = tk.Frame(root)
        main_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, text="7-Segment Display Simulator", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # Create multi-digit display
        self.display = MultiDigitDisplay(main_frame, num_digits=4)
        
        # Control frame
        control_frame = tk.Frame(main_frame)
        control_frame.pack(pady=20)
        
        # Number input
        tk.Label(control_frame, text="Enter Number:").grid(row=0, column=0, padx=5)
        self.number_entry = tk.Entry(control_frame, width=10)
        self.number_entry.grid(row=0, column=1, padx=5)
        self.number_entry.bind('<KeyRelease>', self.on_number_change)
        
        # Text input
        tk.Label(control_frame, text="Enter Text:").grid(row=1, column=0, padx=5)
        self.text_entry = tk.Entry(control_frame, width=10)
        self.text_entry.grid(row=1, column=1, padx=5)
        self.text_entry.bind('<KeyRelease>', self.on_text_change)
        
        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="Show Number", 
                 command=self.show_number).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Show Text", 
                 command=self.show_text).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Clear", 
                 command=self.clear_display).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Demo", 
                 command=self.run_demo).pack(side=tk.LEFT, padx=5)
        
        # Initialize with 0
        self.display.set_number(0)
        
        self.demo_running = False
    
    def on_number_change(self, event):
        """Handle number entry changes"""
        try:
            number = int(self.number_entry.get())
            self.display.set_number(number)
        except ValueError:
            pass
    
    def on_text_change(self, event):
        """Handle text entry changes"""
        text = self.text_entry.get()
        self.display.set_text(text)
    
    def show_number(self):
        """Show the number from entry"""
        try:
            number = int(self.number_entry.get())
            self.display.set_number(number)
        except ValueError:
            tk.messagebox.showerror("Error", "Please enter a valid number")
    
    def show_text(self):
        """Show the text from entry"""
        text = self.text_entry.get()
        self.display.set_text(text)
    
    def clear_display(self):
        """Clear the display"""
        self.display.set_text("    ")
    
    def run_demo(self):
        """Run a demonstration"""
        if not self.demo_running:
            self.demo_running = True
            threading.Thread(target=self.demo_thread, daemon=True).start()
    
    def demo_thread(self):
        """Demo thread function"""
        # Count from 0 to 9999
        for i in range(0, 10000, 111):
            if not self.demo_running:
                break
            self.root.after(0, lambda num=i: self.display.set_number(num))
            time.sleep(0.1)
        
        # Show some text
        texts = ["HELP", "COOL", "CAFE", "BABE", "FACE", "DEAD", "BEEF"]
        for text in texts:
            if not self.demo_running:
                break
            self.root.after(0, lambda t=text: self.display.set_text(t))
            time.sleep(1)
        
        self.demo_running = False

if __name__ == "__main__":
    root = tk.Tk()
    app = SevenSegmentApp(root)
    root.mainloop()
