;===============================================================
; 8051 Configurable Sine Wave Generator
;===============================================================
; This program generates a sine wave through a DAC connected to Port 1
; The sine wave parameters can be configured:
; - Frequency: Controlled by value at memory location 30H (lower = higher frequency)
; - Amplitude: Controlled by value at memory location 31H (0-127)
; - Phase: Controlled by value at memory location 32H (0-63 for full 360 degrees)
;===============================================================

; Define constants
SINE_TABLE_SIZE    EQU     64      ; Size of sine lookup table (64 points)
DAC_PORT           EQU     P1      ; DAC connected to Port 1

; Define memory locations for parameters
FREQ_CONTROL       EQU     30H     ; Frequency control (lower = faster)
AMPLITUDE          EQU     31H     ; Amplitude control (0-127)
PHASE_OFFSET       EQU     32H     ; Phase offset (0-63)
CURRENT_INDEX      EQU     33H     ; Current index in the sine table

; Define memory locations
ORG     0000H           ; Start of program
LJMP    MAIN            ; Jump to main program

; Main program
ORG     0030H
MAIN:
    ; Initialize the stack pointer
    MOV     SP, #7FH
    
    ; Initialize parameters with default values
    MOV     FREQ_CONTROL, #10      ; Default frequency
    MOV     AMPLITUDE, #127        ; Default amplitude (max)
    MOV     PHASE_OFFSET, #0       ; Default phase (0 degrees)
    MOV     CURRENT_INDEX, #0      ; Start at beginning of table
    
    ; Main loop - continuously output sine wave
WAVE_LOOP:
    ; Calculate the index with phase offset
    MOV     A, CURRENT_INDEX
    ADD     A, PHASE_OFFSET        ; Add phase offset
    MOV     R1, A                  ; Store in R1
    
    ; Handle wrap-around for index
    CLR     C
    SUBB    A, #SINE_TABLE_SIZE
    JC      NO_WRAP
    MOV     A, R1
    SUBB    A, #SINE_TABLE_SIZE
    MOV     R1, A
NO_WRAP:
    
    ; Get the sine value from the table
    MOV     DPTR, #SINE_TABLE
    MOV     A, R1
    MOVC    A, @A+DPTR             ; Get sine value from table
    
    ; Apply amplitude scaling
    ; First, center the sine value around 0 (subtract 128)
    SUBB    A, #128
    JNC     POSITIVE_VALUE
    CPL     A                      ; 2's complement for negative values
    ADD     A, #1
    MOV     R2, A                  ; Store absolute value in R2
    MOV     R3, #1                 ; Flag for negative value
    SJMP    SCALE_AMPLITUDE
POSITIVE_VALUE:
    MOV     R2, A                  ; Store absolute value in R2
    MOV     R3, #0                 ; Flag for positive value
    
SCALE_AMPLITUDE:
    ; Scale by amplitude (R2 = R2 * AMPLITUDE / 127)
    MOV     A, R2
    MOV     B, AMPLITUDE
    MUL     AB                     ; A = A * B
    MOV     B, #127
    DIV     AB                     ; A = A / B
    MOV     R2, A                  ; Scaled absolute value
    
    ; Convert back to signed and then to 0-255 range
    MOV     A, R3
    JZ      WAS_POSITIVE
    MOV     A, R2
    CPL     A                      ; 2's complement to get negative value back
    ADD     A, #1
    SJMP    CONVERT_TO_DAC
WAS_POSITIVE:
    MOV     A, R2
    
CONVERT_TO_DAC:
    ; Convert from -128..127 to 0..255 for DAC
    ADD     A, #128
    
    ; Output to DAC
    MOV     DAC_PORT, A
    
    ; Delay for frequency control
    ACALL   DELAY
    
    ; Increment index and check if we've completed one cycle
    INC     CURRENT_INDEX
    MOV     A, CURRENT_INDEX
    CJNE    A, #SINE_TABLE_SIZE, CONTINUE_WAVE
    MOV     CURRENT_INDEX, #0      ; Reset index for next cycle
    
CONTINUE_WAVE:
    ; Check if parameters have been updated (could be done via interrupts in a real system)
    ; For this example, we just continue with the wave generation
    SJMP    WAVE_LOOP

;===============================================================
; Delay subroutine - controls the frequency of the sine wave
;===============================================================
DELAY:
    MOV     R7, FREQ_CONTROL
DELAY_LOOP1:
    MOV     R6, #50
DELAY_LOOP2:
    DJNZ    R6, DELAY_LOOP2
    DJNZ    R7, DELAY_LOOP1
    RET

;===============================================================
; Sine wave lookup table (64 points, 8-bit values)
;===============================================================
ORG     0200H
SINE_TABLE:
    DB      128, 140, 152, 165, 176, 188, 198, 208
    DB      217, 225, 233, 239, 244, 248, 251, 253
    DB      254, 253, 251, 248, 244, 239, 233, 225
    DB      217, 208, 198, 188, 176, 165, 152, 140
    DB      128, 116, 104, 91, 80, 68, 58, 48
    DB      39, 31, 23, 17, 12, 8, 5, 3
    DB      2, 3, 5, 8, 12, 17, 23, 31
    DB      39, 48, 58, 68, 80, 91, 104, 116

END
