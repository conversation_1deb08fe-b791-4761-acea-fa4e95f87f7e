/*
 * 7-Segment Display Controller for Arduino
 * Supports common cathode and common anode displays
 */

// Pin definitions for 7-segment display
// Modify these according to your wiring
const int segmentPins[7] = {2, 3, 4, 5, 6, 7, 8}; // a, b, c, d, e, f, g
const int digitPins[4] = {9, 10, 11, 12};          // For multi-digit displays

// Set to true for common anode, false for common cathode
const bool COMMON_ANODE = false;

// Segment patterns for digits 0-9 and letters A-F
// Each byte represents segments a-g (bit 0 = segment a, bit 6 = segment g)
const byte digitPatterns[16] = {
  0b00111111, // 0
  0b00000110, // 1
  0b01011011, // 2
  0b01001111, // 3
  0b01100110, // 4
  0b01101101, // 5
  0b01111101, // 6
  0b00000111, // 7
  0b01111111, // 8
  0b01101111, // 9
  0b01110111, // A
  0b01111100, // B
  0b00111001, // C
  0b01011110, // D
  0b01111001, // E
  0b01110001  // F
};

// Special patterns
const byte PATTERN_DASH = 0b01000000;  // -
const byte PATTERN_BLANK = 0b00000000; // blank

class SevenSegmentDisplay {
private:
  int currentDigit;
  unsigned long lastUpdateTime;
  int displayValue;
  bool blinkState;
  unsigned long blinkInterval;
  
public:
  SevenSegmentDisplay() {
    currentDigit = 0;
    lastUpdateTime = 0;
    displayValue = 0;
    blinkState = false;
    blinkInterval = 500; // 500ms blink interval
  }
  
  void begin() {
    // Initialize segment pins as outputs
    for (int i = 0; i < 7; i++) {
      pinMode(segmentPins[i], OUTPUT);
    }
    
    // Initialize digit pins as outputs
    for (int i = 0; i < 4; i++) {
      pinMode(digitPins[i], OUTPUT);
      digitalWrite(digitPins[i], COMMON_ANODE ? HIGH : LOW); // Turn off all digits
    }
    
    // Clear display
    clearDisplay();
  }
  
  void displayDigit(int digit, byte pattern) {
    // Turn off all digits first
    for (int i = 0; i < 4; i++) {
      digitalWrite(digitPins[i], COMMON_ANODE ? HIGH : LOW);
    }
    
    // Set segment pattern
    for (int i = 0; i < 7; i++) {
      bool segmentOn = (pattern >> i) & 1;
      if (COMMON_ANODE) {
        segmentOn = !segmentOn; // Invert for common anode
      }
      digitalWrite(segmentPins[i], segmentOn ? HIGH : LOW);
    }
    
    // Turn on the selected digit
    if (digit >= 0 && digit < 4) {
      digitalWrite(digitPins[digit], COMMON_ANODE ? LOW : HIGH);
    }
  }
  
  void clearDisplay() {
    for (int i = 0; i < 4; i++) {
      digitalWrite(digitPins[i], COMMON_ANODE ? HIGH : LOW);
    }
    for (int i = 0; i < 7; i++) {
      digitalWrite(segmentPins[i], LOW);
    }
  }
  
  void showNumber(int number) {
    displayValue = number;
    
    // Handle negative numbers
    bool isNegative = number < 0;
    if (isNegative) {
      number = -number;
    }
    
    // Extract digits
    int digits[4];
    digits[0] = number % 10;           // ones
    digits[1] = (number / 10) % 10;    // tens
    digits[2] = (number / 100) % 10;   // hundreds
    digits[3] = (number / 1000) % 10;  // thousands
    
    // Display each digit with multiplexing
    static unsigned long lastMuxTime = 0;
    static int currentMuxDigit = 0;
    
    if (millis() - lastMuxTime > 5) { // 5ms per digit = 200Hz refresh
      // Determine what to show on current digit
      byte pattern = PATTERN_BLANK;
      
      if (isNegative && currentMuxDigit == 3) {
        pattern = PATTERN_DASH; // Show minus sign on leftmost digit
      } else if (number > 0 || currentMuxDigit == 0) {
        // Show digit if number is not zero, or always show ones place
        int digitValue = digits[currentMuxDigit];
        if (number >= pow(10, currentMuxDigit) || currentMuxDigit == 0) {
          pattern = digitPatterns[digitValue];
        }
      }
      
      displayDigit(currentMuxDigit, pattern);
      
      currentMuxDigit = (currentMuxDigit + 1) % 4;
      lastMuxTime = millis();
    }
  }
  
  void showHex(int number) {
    // Display number in hexadecimal
    static unsigned long lastMuxTime = 0;
    static int currentMuxDigit = 0;
    
    if (millis() - lastMuxTime > 5) {
      int digitValue = (number >> (currentMuxDigit * 4)) & 0xF;
      byte pattern = digitPatterns[digitValue];
      
      displayDigit(3 - currentMuxDigit, pattern); // Display from left to right
      
      currentMuxDigit = (currentMuxDigit + 1) % 4;
      lastMuxTime = millis();
    }
  }
  
  void showText(String text) {
    // Show text (limited to available patterns)
    static unsigned long lastMuxTime = 0;
    static int currentMuxDigit = 0;
    
    if (millis() - lastMuxTime > 5) {
      byte pattern = PATTERN_BLANK;
      
      if (currentMuxDigit < text.length()) {
        char c = text.charAt(text.length() - 1 - currentMuxDigit);
        
        if (c >= '0' && c <= '9') {
          pattern = digitPatterns[c - '0'];
        } else if (c >= 'A' && c <= 'F') {
          pattern = digitPatterns[c - 'A' + 10];
        } else if (c >= 'a' && c <= 'f') {
          pattern = digitPatterns[c - 'a' + 10];
        } else if (c == '-') {
          pattern = PATTERN_DASH;
        }
      }
      
      displayDigit(currentMuxDigit, pattern);
      
      currentMuxDigit = (currentMuxDigit + 1) % 4;
      lastMuxTime = millis();
    }
  }
  
  void blinkDisplay(bool enable, unsigned long interval = 500) {
    blinkInterval = interval;
    
    if (enable) {
      if (millis() - lastUpdateTime > blinkInterval) {
        blinkState = !blinkState;
        lastUpdateTime = millis();
      }
      
      if (blinkState) {
        clearDisplay();
      } else {
        showNumber(displayValue);
      }
    }
  }
};

// Global display object
SevenSegmentDisplay display;

void setup() {
  Serial.begin(9600);
  display.begin();
  
  Serial.println("7-Segment Display Controller");
  Serial.println("Commands:");
  Serial.println("  n<number> - Show number (e.g., n1234)");
  Serial.println("  h<hex>    - Show hex (e.g., hABCD)");
  Serial.println("  t<text>   - Show text (e.g., tCAFE)");
  Serial.println("  c         - Clear display");
  Serial.println("  d         - Demo mode");
}

void loop() {
  // Handle serial commands
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    
    if (command.length() > 0) {
      char cmd = command.charAt(0);
      String value = command.substring(1);
      
      switch (cmd) {
        case 'n':
        case 'N':
          display.showNumber(value.toInt());
          Serial.println("Showing number: " + value);
          break;
          
        case 'h':
        case 'H':
          display.showHex(strtol(value.c_str(), NULL, 16));
          Serial.println("Showing hex: " + value);
          break;
          
        case 't':
        case 'T':
          display.showText(value);
          Serial.println("Showing text: " + value);
          break;
          
        case 'c':
        case 'C':
          display.clearDisplay();
          Serial.println("Display cleared");
          break;
          
        case 'd':
        case 'D':
          runDemo();
          break;
          
        default:
          Serial.println("Unknown command: " + command);
          break;
      }
    }
  }
  
  // Default behavior - show current time in seconds
  static unsigned long lastTimeUpdate = 0;
  if (millis() - lastTimeUpdate > 1000) {
    int seconds = (millis() / 1000) % 10000; // Show last 4 digits of seconds
    display.showNumber(seconds);
    lastTimeUpdate = millis();
  }
}

void runDemo() {
  Serial.println("Running demo...");
  
  // Count from 0 to 99
  for (int i = 0; i <= 99; i++) {
    display.showNumber(i);
    delay(100);
  }
  
  // Show hex values
  for (int i = 0; i <= 0xFF; i += 0x11) {
    display.showHex(i);
    delay(500);
  }
  
  // Show some text
  String texts[] = {"CAFE", "BABE", "FACE", "DEAD", "BEEF", "FADE"};
  for (int i = 0; i < 6; i++) {
    display.showText(texts[i]);
    delay(1000);
  }
  
  Serial.println("Demo complete");
}
