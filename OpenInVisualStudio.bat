@echo off
echo Opening SineWaveDeviceControl project in Visual Studio...

:: Try to find Visual Studio installation
set VS_PATH=

:: Check for Visual Studio 2022
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe" (
    set VS_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe"
    goto found
)

:: Check for Visual Studio 2019
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe" (
    set VS_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe"
    goto found
)

:: Check for Visual Studio 2017
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Community\Common7\IDE\devenv.exe" (
    set VS_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Community\Common7\IDE\devenv.exe"
    goto found
)

:: Check for older Visual Studio versions
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio 14.0\Common7\IDE\devenv.exe" (
    set VS_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio 14.0\Common7\IDE\devenv.exe"
    goto found
)

:not_found
echo Visual Studio not found. Please open the solution manually.
echo Solution file: %~dp0SineWaveDeviceControl.sln
goto end

:found
echo Found Visual Studio at %VS_PATH%
echo Opening solution...
%VS_PATH% "%~dp0SineWaveDeviceControl.sln"

:end
pause
