;===============================================================
; 8051 Sine Wave Generator
;===============================================================
; This program generates a sine wave through a DAC connected to Port 1
; The sine wave is generated using a lookup table with 256 values
; The frequency can be adjusted by changing the delay value
;===============================================================

; Define constants
SINE_TABLE_SIZE    EQU     64      ; Size of sine lookup table (64 points)
DAC_PORT           EQU     P1      ; DAC connected to Port 1
FREQUENCY_CONTROL  EQU     30H     ; Memory location for frequency control (lower = faster)

; Define memory locations
ORG     0000H           ; Start of program
LJMP    MAIN            ; Jump to main program

; Main program
ORG     0030H
MAIN:
    ; Initialize the stack pointer
    MOV     SP, #7FH
    
    ; Initialize frequency control (default value)
    MOV     FREQUENCY_CONTROL, #10  ; Adjust for desired frequency
    
    ; Main loop - continuously output sine wave
WAVE_LOOP:
    MOV     DPTR, #SINE_TABLE       ; Point to sine table
    MOV     R0, #0                  ; Initialize index
    
OUTPUT_LOOP:
    MOV     A, R0                   ; Get current index
    MOVC    A, @A+DPTR              ; Get sine value from table
    MOV     DAC_PORT, A             ; Output to DAC
    
    ; Delay for frequency control
    ACALL   DELAY
    
    ; Increment index and check if we've completed one cycle
    INC     R0
    CJNE    R0, #SINE_TABLE_SIZE, OUTPUT_LOOP
    
    ; Repeat the wave generation
    SJMP    WAVE_LOOP

;===============================================================
; Delay subroutine - controls the frequency of the sine wave
;===============================================================
DELAY:
    MOV     R7, FREQUENCY_CONTROL
DELAY_LOOP1:
    MOV     R6, #50
DELAY_LOOP2:
    DJNZ    R6, DELAY_LOOP2
    DJNZ    R7, DELAY_LOOP1
    RET

;===============================================================
; Sine wave lookup table (64 points, 8-bit values)
;===============================================================
ORG     0200H
SINE_TABLE:
    DB      128, 140, 152, 165, 176, 188, 198, 208
    DB      217, 225, 233, 239, 244, 248, 251, 253
    DB      254, 253, 251, 248, 244, 239, 233, 225
    DB      217, 208, 198, 188, 176, 165, 152, 140
    DB      128, 116, 104, 91, 80, 68, 58, 48
    DB      39, 31, 23, 17, 12, 8, 5, 3
    DB      2, 3, 5, 8, 12, 17, 23, 31
    DB      39, 48, 58, 68, 80, 91, 104, 116

END
