import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from matplotlib.widgets import <PERSON><PERSON><PERSON>, Button, RadioButtons, CheckButtons

def create_configurable_sine_wave():
    """
    Create a sine wave visualization with GUI controls to configure
    frequency, phase, amplitude, and other parameters.
    """
    # Initial parameters
    params = {
        'frequency': 1.0,
        'amplitude': 1.0,
        'phase_offset': 0.0,
        'animation_speed': 0.05,
        'wave_color': 'blue',
        'show_points': True,
        'show_grid': True,
        'wave_type': 'sine'  # 'sine', 'cosine', 'square', 'sawtooth', 'triangle'
    }
    
    # Create figure and axes
    fig = plt.figure(figsize=(12, 8))
    fig.suptitle('Configurable Wave Visualization', fontsize=16)
    
    # Create main plot area (top 70% of figure)
    ax_main = plt.axes([0.1, 0.35, 0.8, 0.55])
    ax_main.set_xlim(0, 2*np.pi)
    ax_main.set_ylim(-1.5, 1.5)
    ax_main.set_xlabel('Angle (radians)')
    ax_main.set_ylabel('Amplitude')
    ax_main.grid(params['show_grid'])
    
    # Add a horizontal line at y=0
    ax_main.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    
    # Create a line object for the wave
    line, = ax_main.plot([], [], linewidth=2, color=params['wave_color'])
    
    # Create a scatter object for points on the wave
    points, = ax_main.plot([], [], 'ro', markersize=6, alpha=0.7)
    
    # Add text for displaying the equation
    equation_text = ax_main.text(0.02, 0.95, '', transform=ax_main.transAxes, 
                               fontsize=10, verticalalignment='top',
                               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Create x data (fixed)
    x = np.linspace(0, 2*np.pi, 1000)
    
    # Create sliders
    slider_color = 'lightgoldenrodyellow'
    
    # Frequency slider
    ax_freq = plt.axes([0.25, 0.25, 0.65, 0.03], facecolor=slider_color)
    freq_slider = Slider(
        ax=ax_freq,
        label='Frequency (Hz)',
        valmin=0.1,
        valmax=5.0,
        valinit=params['frequency'],
        valstep=0.1
    )
    
    # Amplitude slider
    ax_amp = plt.axes([0.25, 0.20, 0.65, 0.03], facecolor=slider_color)
    amp_slider = Slider(
        ax=ax_amp,
        label='Amplitude',
        valmin=0.1,
        valmax=1.5,
        valinit=params['amplitude'],
        valstep=0.1
    )
    
    # Phase slider
    ax_phase = plt.axes([0.25, 0.15, 0.65, 0.03], facecolor=slider_color)
    phase_slider = Slider(
        ax=ax_phase,
        label='Phase Offset (rad)',
        valmin=0,
        valmax=2*np.pi,
        valinit=params['phase_offset'],
        valstep=0.1
    )
    
    # Animation speed slider
    ax_speed = plt.axes([0.25, 0.10, 0.65, 0.03], facecolor=slider_color)
    speed_slider = Slider(
        ax=ax_speed,
        label='Animation Speed',
        valmin=0.01,
        valmax=0.2,
        valinit=params['animation_speed'],
        valstep=0.01
    )
    
    # Reset button
    ax_reset = plt.axes([0.8, 0.025, 0.1, 0.04])
    reset_button = Button(ax_reset, 'Reset', color=slider_color, hovercolor='0.975')
    
    # Wave type radio buttons
    ax_wave_type = plt.axes([0.025, 0.10, 0.15, 0.15], facecolor=slider_color)
    wave_type_radio = RadioButtons(
        ax_wave_type,
        ('sine', 'cosine', 'square', 'sawtooth', 'triangle'),
        active=0
    )
    
    # Color radio buttons
    ax_color = plt.axes([0.025, 0.30, 0.15, 0.15], facecolor=slider_color)
    color_radio = RadioButtons(
        ax_color,
        ('blue', 'red', 'green', 'purple', 'orange'),
        active=0
    )
    
    # Check buttons for display options
    ax_check = plt.axes([0.025, 0.50, 0.15, 0.10], facecolor=slider_color)
    check_buttons = CheckButtons(
        ax_check,
        ['Show Points', 'Show Grid'],
        [params['show_points'], params['show_grid']]
    )
    
    # Function to calculate wave based on type
    def calculate_wave(x, frame_phase, wave_type):
        phase = frame_phase + params['phase_offset']
        if wave_type == 'sine':
            return params['amplitude'] * np.sin(params['frequency'] * x - phase)
        elif wave_type == 'cosine':
            return params['amplitude'] * np.cos(params['frequency'] * x - phase)
        elif wave_type == 'square':
            return params['amplitude'] * np.sign(np.sin(params['frequency'] * x - phase))
        elif wave_type == 'sawtooth':
            t = (params['frequency'] * x - phase) / (2 * np.pi)
            return params['amplitude'] * (2 * (t - np.floor(0.5 + t)))
        elif wave_type == 'triangle':
            t = (params['frequency'] * x - phase) / (2 * np.pi)
            return params['amplitude'] * (2 * np.abs(2 * (t - np.floor(t + 0.5))) - 1)
    
    # Function to get the equation text based on wave type
    def get_equation_text():
        eq = f"y = {params['amplitude']:.1f} × "
        if params['wave_type'] == 'sine':
            eq += f"sin({params['frequency']:.1f}x - φ)"
        elif params['wave_type'] == 'cosine':
            eq += f"cos({params['frequency']:.1f}x - φ)"
        elif params['wave_type'] == 'square':
            eq += f"sgn(sin({params['frequency']:.1f}x - φ))"
        elif params['wave_type'] == 'sawtooth':
            eq += f"sawtooth({params['frequency']:.1f}x - φ)"
        elif params['wave_type'] == 'triangle':
            eq += f"triangle({params['frequency']:.1f}x - φ)"
        
        eq += f"\nφ = {params['phase_offset']:.1f} rad"
        return eq
    
    # Animation phase (separate from the phase offset parameter)
    animation_phase = 0
    
    # Function to initialize the animation
    def init():
        line.set_data([], [])
        points.set_data([], [])
        equation_text.set_text('')
        return line, points, equation_text
    
    # Function to update the animation for each frame
    def update(frame):
        nonlocal animation_phase
        
        # Update animation phase
        animation_phase = (animation_phase + params['animation_speed']) % (2*np.pi)
        
        # Calculate the wave
        y = calculate_wave(x, animation_phase, params['wave_type'])
        
        # Update the line
        line.set_data(x, y)
        
        # Create points at specific angles if enabled
        if params['show_points']:
            point_angles = np.linspace(0, 2*np.pi, 12, endpoint=False)
            point_y = calculate_wave(point_angles, animation_phase, params['wave_type'])
            points.set_data(point_angles, point_y)
        else:
            points.set_data([], [])
        
        # Update the equation text
        equation_text.set_text(get_equation_text())
        
        return line, points, equation_text
    
    # Callback for frequency slider
    def update_frequency(val):
        params['frequency'] = val
    
    # Callback for amplitude slider
    def update_amplitude(val):
        params['amplitude'] = val
        # Update y-axis limits based on amplitude
        ax_main.set_ylim(-params['amplitude']*1.5, params['amplitude']*1.5)
    
    # Callback for phase slider
    def update_phase(val):
        params['phase_offset'] = val
    
    # Callback for animation speed slider
    def update_speed(val):
        params['animation_speed'] = val
    
    # Callback for reset button
    def reset(event):
        freq_slider.reset()
        amp_slider.reset()
        phase_slider.reset()
        speed_slider.reset()
        params['frequency'] = 1.0
        params['amplitude'] = 1.0
        params['phase_offset'] = 0.0
        params['animation_speed'] = 0.05
        params['wave_type'] = 'sine'
        params['wave_color'] = 'blue'
        params['show_points'] = True
        params['show_grid'] = True
        
        # Reset radio buttons
        wave_type_radio.set_active(0)
        color_radio.set_active(0)
        
        # Reset check buttons
        check_buttons.set_active(0, True)
        check_buttons.set_active(1, True)
        
        # Update the plot
        line.set_color('blue')
        ax_main.grid(True)
        
        # Reset y-axis limits
        ax_main.set_ylim(-1.5, 1.5)
    
    # Callback for wave type radio buttons
    def update_wave_type(label):
        params['wave_type'] = label
    
    # Callback for color radio buttons
    def update_color(label):
        params['wave_color'] = label
        line.set_color(label)
    
    # Callback for check buttons
    def update_display(label):
        if label == 'Show Points':
            params['show_points'] = not params['show_points']
        elif label == 'Show Grid':
            params['show_grid'] = not params['show_grid']
            ax_main.grid(params['show_grid'])
    
    # Register callbacks
    freq_slider.on_changed(update_frequency)
    amp_slider.on_changed(update_amplitude)
    phase_slider.on_changed(update_phase)
    speed_slider.on_changed(update_speed)
    reset_button.on_clicked(reset)
    wave_type_radio.on_clicked(update_wave_type)
    color_radio.on_clicked(update_color)
    check_buttons.on_clicked(update_display)
    
    # Create the animation
    ani = FuncAnimation(fig, update, frames=range(1000), 
                        init_func=init, blit=True, interval=30,
                        repeat=True)
    
    # Add a note about closing the window
    plt.figtext(0.5, 0.01, 'Close this window manually when done', 
                ha='center', fontsize=10, 
                bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.5))
    
    # Show the plot (this will block until the window is closed)
    plt.tight_layout(rect=[0, 0.05, 1, 0.95])
    plt.show()

if __name__ == "__main__":
    print("Starting configurable wave visualization...")
    print("The window will stay open until you manually close it.")
    create_configurable_sine_wave()
    print("Visualization window closed.")
