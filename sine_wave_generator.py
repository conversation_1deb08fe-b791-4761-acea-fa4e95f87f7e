import numpy as np
import matplotlib.pyplot as plt
from scipy.io import wavfile
import os

class SineWaveGenerator:
    """
    A class for generating, visualizing, and saving sine waves.
    
    This class provides functionality to create sine waves with customizable
    parameters such as frequency, amplitude, phase, and duration.
    """
    
    def __init__(self, sampling_rate=44100):
        """
        Initialize the sine wave generator.
        
        Parameters:
        -----------
        sampling_rate : int, optional
            The number of samples per second (Hz), by default 44100 (CD quality)
        """
        self.sampling_rate = sampling_rate
    
    def generate_sine_wave(self, frequency, amplitude=1.0, phase=0.0, duration=1.0):
        """
        Generate a sine wave with the specified parameters.
        
        Parameters:
        -----------
        frequency : float
            The frequency of the sine wave in Hz
        amplitude : float, optional
            The peak amplitude of the sine wave, by default 1.0
        phase : float, optional
            The phase offset in radians, by default 0.0
        duration : float, optional
            The duration of the sine wave in seconds, by default 1.0
            
        Returns:
        --------
        tuple
            A tuple containing (time_array, sine_wave_array)
        """
        # Create time array
        num_samples = int(self.sampling_rate * duration)
        time_array = np.linspace(0, duration, num_samples, endpoint=False)
        
        # Generate sine wave
        sine_wave = amplitude * np.sin(2 * np.pi * frequency * time_array + phase)
        
        return time_array, sine_wave
    
    def plot_wave(self, time_array, wave_array, title="Sine Wave"):
        """
        Plot the generated wave.
        
        Parameters:
        -----------
        time_array : numpy.ndarray
            Array of time points
        wave_array : numpy.ndarray
            Array of wave amplitude values
        title : str, optional
            Title for the plot, by default "Sine Wave"
        """
        plt.figure(figsize=(10, 6))
        plt.plot(time_array, wave_array)
        plt.title(title)
        plt.xlabel('Time (s)')
        plt.ylabel('Amplitude')
        plt.grid(True)
        plt.show()
    
    def save_as_wav(self, wave_array, filename="sine_wave.wav"):
        """
        Save the wave as a WAV audio file.
        
        Parameters:
        -----------
        wave_array : numpy.ndarray
            Array of wave amplitude values
        filename : str, optional
            Name of the output file, by default "sine_wave.wav"
            
        Notes:
        ------
        The wave is normalized to the range [-1, 1] and then scaled to 16-bit PCM range.
        """
        # Normalize to range [-1, 1]
        normalized_wave = wave_array / np.max(np.abs(wave_array))
        
        # Convert to 16-bit PCM
        audio_data = (normalized_wave * 32767).astype(np.int16)
        
        # Save as WAV file
        wavfile.write(filename, self.sampling_rate, audio_data)
        print(f"Wave saved as {os.path.abspath(filename)}")
    
    def generate_chord(self, frequencies, amplitudes=None, phases=None, duration=1.0):
        """
        Generate a chord (sum of multiple sine waves).
        
        Parameters:
        -----------
        frequencies : list of float
            List of frequencies in Hz for each sine wave
        amplitudes : list of float, optional
            List of amplitudes for each sine wave. If None, all amplitudes are set to 1.0
        phases : list of float, optional
            List of phase offsets in radians for each sine wave. If None, all phases are set to 0.0
        duration : float, optional
            The duration of the chord in seconds, by default 1.0
            
        Returns:
        --------
        tuple
            A tuple containing (time_array, chord_wave_array)
        """
        if amplitudes is None:
            amplitudes = [1.0] * len(frequencies)
        
        if phases is None:
            phases = [0.0] * len(frequencies)
        
        # Create time array
        num_samples = int(self.sampling_rate * duration)
        time_array = np.linspace(0, duration, num_samples, endpoint=False)
        
        # Initialize chord array
        chord = np.zeros(num_samples)
        
        # Add each sine wave to the chord
        for freq, amp, phase in zip(frequencies, amplitudes, phases):
            sine_wave = amp * np.sin(2 * np.pi * freq * time_array + phase)
            chord += sine_wave
        
        return time_array, chord


# Example usage
if __name__ == "__main__":
    # Create a sine wave generator
    generator = SineWaveGenerator(sampling_rate=44100)
    
    # Example 1: Generate a simple 440 Hz sine wave (A4 note)
    print("Generating a 440 Hz sine wave (A4 note)...")
    time, wave = generator.generate_sine_wave(frequency=440, amplitude=0.8, duration=2.0)
    generator.plot_wave(time, wave, title="440 Hz Sine Wave (A4 note)")
    generator.save_as_wav(wave, "A4_note.wav")
    
    # Example 2: Generate a chord (C major: C4, E4, G4)
    print("\nGenerating a C major chord...")
    c_major_frequencies = [261.63, 329.63, 392.00]  # C4, E4, G4
    amplitudes = [0.6, 0.5, 0.5]  # Slightly emphasize the root note
    time, chord = generator.generate_chord(
        frequencies=c_major_frequencies,
        amplitudes=amplitudes,
        duration=3.0
    )
    generator.plot_wave(time, chord, title="C Major Chord (C4, E4, G4)")
    generator.save_as_wav(chord, "C_major_chord.wav")
    
    print("\nExamples completed. Check the generated WAV files.")
