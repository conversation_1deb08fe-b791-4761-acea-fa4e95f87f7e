# Sine Wave Generator

A standalone Windows application for generating and visualizing sine waves with configurable parameters.

## Features

- **Interactive Visualization**: Real-time animated sine wave display
- **Multiple Wave Types**: Sine, Cosine, Square, Sawtooth, and Triangle waves
- **Configurable Parameters**:
  - Frequency (0.1 to 5.0 Hz)
  - Amplitude (0.1 to 1.5)
  - Phase Offset (0 to 2π radians)
  - Animation Speed
- **Display Options**:
  - Wave Color Selection
  - Toggle Grid Lines
  - Toggle Point Markers
- **Mathematical Equation**: Shows the equation for the current wave

## Installation

No installation required! Simply download and run the executable file.

## How to Use

1. **Launch the Application**:
   - Double-click on `SineWaveGenerator.exe` to start the program
   - A window will open showing the sine wave visualization

2. **Adjust Parameters**:
   - Use the sliders to change Frequency, Amplitude, Phase, and Animation Speed
   - Select different wave types using the radio buttons on the left
   - Change the wave color using the color selection radio buttons
   - Toggle display options using the checkboxes

3. **Reset to Defaults**:
   - Click the "Reset" button to return all settings to their default values

4. **Close the Application**:
   - Close the window when you're done

## System Requirements

- Windows 7/8/10/11
- No additional software required (all dependencies are included in the executable)

## Technical Details

This application was created using:
- Python 3.12
- Matplotlib for visualization
- NumPy for numerical calculations
- PyInstaller for creating the standalone executable

## Troubleshooting

If the application doesn't start:
1. Make sure you have administrative privileges
2. Try running the application in compatibility mode
3. Check if your antivirus is blocking the application

## License

This software is provided as-is for educational and demonstration purposes.
