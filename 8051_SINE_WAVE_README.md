# 8051 Sine Wave Generator

This package contains files for generating sine waves using an 8051 microcontroller.

## Files Included

1. `sine_wave_8051.asm` - Assembly source code for basic sine wave generation
2. `sine_wave_8051.hex` - Compiled HEX file for basic sine wave generation
3. `configurable_sine_wave_8051.asm` - Assembly source code for configurable sine wave generation
4. `configurable_sine_wave_8051.hex` - Compiled HEX file for configurable sine wave generation

## Hardware Requirements

1. 8051 microcontroller (or compatible)
2. Digital-to-Analog Converter (DAC) connected to Port 1
3. Programmer for loading HEX files to the microcontroller
4. Optional: Oscilloscope for visualizing the output

## Circuit Connection

```
8051 Microcontroller    DAC (e.g., DAC0808)
-----------------       ---------------
P1.0 ----------------> D0
P1.1 ----------------> D1
P1.2 ----------------> D2
P1.3 ----------------> D3
P1.4 ----------------> D4
P1.5 ----------------> D5
P1.6 ----------------> D6
P1.7 ----------------> D7
```

## Basic Sine Wave Generator

The basic sine wave generator (`sine_wave_8051.hex`) produces a fixed sine wave with:
- 64 samples per cycle
- 8-bit resolution
- Fixed frequency (determined by the delay routine)

## Configurable Sine Wave Generator

The configurable sine wave generator (`configurable_sine_wave_8051.hex`) allows you to adjust:

1. **Frequency**: Controlled by the value at memory location 30H
   - Lower values = higher frequency
   - Higher values = lower frequency
   - Default: 10

2. **Amplitude**: Controlled by the value at memory location 31H
   - Range: 0-127 (0 = no output, 127 = full amplitude)
   - Default: 127 (maximum)

3. **Phase**: Controlled by the value at memory location 32H
   - Range: 0-63 (representing 0-360 degrees)
   - Default: 0

## How to Load and Configure

1. Load the appropriate HEX file to your 8051 microcontroller using your programmer
2. For the configurable version, you can modify the parameters at runtime:
   - Use your debugger to write to memory locations 30H, 31H, and 32H
   - Or modify your circuit to allow external input to these memory locations

## Example: Changing Parameters

To change the frequency to a higher value:
```
MOV 30H, #5    ; Higher frequency (lower delay value)
```

To reduce the amplitude to half:
```
MOV 31H, #64   ; Approximately half amplitude
```

To shift the phase by 90 degrees:
```
MOV 32H, #16   ; 90-degree phase shift (16 = 64/4)
```

## Notes

- The sine wave is generated using a lookup table with 64 points
- The output is 8-bit (0-255) suitable for most DACs
- The frequency is controlled by a software delay routine
- For precise frequency control, consider using a timer interrupt

## Customization

If you need to modify the code:
1. Edit the assembly source files
2. Assemble using an 8051 assembler (e.g., ASEM-51, A51, etc.)
3. Load the resulting HEX file to your microcontroller
