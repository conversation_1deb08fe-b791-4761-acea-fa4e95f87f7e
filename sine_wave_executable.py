import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from matplotlib.widgets import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, RadioButtons, CheckButtons
import sys
import os

# Set matplotlib backend to ensure GUI works
import matplotlib
matplotlib.use('TkAgg')

def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

print("Starting Professional Sine Wave Generator...")
print("Checking dependencies...")
try:
    import numpy as np
    print("✓ NumPy imported successfully")
    import matplotlib.pyplot as plt
    print("✓ Matplotlib imported successfully")
    from matplotlib.animation import FuncAnimation
    print("✓ Animation module imported successfully")
    from matplotlib.widgets import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, RadioButtons, CheckButtons
    print("✓ Widget modules imported successfully")
except ImportError as e:
    print(f"✗ Import error: {e}")
    input("Press Enter to exit...")
    sys.exit(1)

class SineWaveExecutableApp:
    """
    Complete Sine Wave Generator Application for Executable
    """
    
    def __init__(self):
        self.params = {
            'frequency': 1.0,
            'amplitude': 1.0,
            'phase_offset': 0.0,
            'animation_speed': 0.05,
            'wave_color': 'blue',
            'show_points': True,
            'show_grid': True,
            'wave_type': 'sine'
        }
        self.animation_phase = 0
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Create figure with professional styling
        plt.style.use('default')
        self.fig = plt.figure(figsize=(14, 9))
        self.fig.canvas.manager.set_window_title('Professional Sine Wave Generator v2.0')
        self.fig.suptitle('Professional Sine Wave Generator', fontsize=18, fontweight='bold')
        
        # Create main plot area
        self.ax_main = plt.axes([0.1, 0.4, 0.8, 0.5])
        self.ax_main.set_xlim(0, 2*np.pi)
        self.ax_main.set_ylim(-1.5, 1.5)
        self.ax_main.set_xlabel('Angle (radians)', fontsize=12)
        self.ax_main.set_ylabel('Amplitude', fontsize=12)
        self.ax_main.grid(self.params['show_grid'], alpha=0.3)
        
        # Add reference line at y=0
        self.ax_main.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        
        # Create wave line and points
        self.line, = self.ax_main.plot([], [], linewidth=3, color=self.params['wave_color'])
        self.points, = self.ax_main.plot([], [], 'ro', markersize=8, alpha=0.8)
        
        # Add equation display
        self.equation_text = self.ax_main.text(0.02, 0.95, '', transform=self.ax_main.transAxes, 
                                             fontsize=12, verticalalignment='top',
                                             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        # Create x data
        self.x = np.linspace(0, 2*np.pi, 1000)
        
        self.create_controls()
        self.setup_callbacks()
        self.start_animation()
    
    def create_controls(self):
        """Create all UI controls"""
        slider_color = 'lightsteelblue'
        
        # Frequency slider
        ax_freq = plt.axes([0.25, 0.30, 0.65, 0.03], facecolor=slider_color)
        self.freq_slider = Slider(ax_freq, 'Frequency (Hz)', 0.1, 10.0, 
                                 valinit=self.params['frequency'], valstep=0.1)
        
        # Amplitude slider
        ax_amp = plt.axes([0.25, 0.25, 0.65, 0.03], facecolor=slider_color)
        self.amp_slider = Slider(ax_amp, 'Amplitude', 0.1, 2.0, 
                                valinit=self.params['amplitude'], valstep=0.1)
        
        # Phase slider
        ax_phase = plt.axes([0.25, 0.20, 0.65, 0.03], facecolor=slider_color)
        self.phase_slider = Slider(ax_phase, 'Phase Offset (rad)', 0, 2*np.pi, 
                                  valinit=self.params['phase_offset'], valstep=0.1)
        
        # Animation speed slider
        ax_speed = plt.axes([0.25, 0.15, 0.65, 0.03], facecolor=slider_color)
        self.speed_slider = Slider(ax_speed, 'Animation Speed', 0.01, 0.3, 
                                  valinit=self.params['animation_speed'], valstep=0.01)
        
        # Control buttons
        ax_reset = plt.axes([0.25, 0.05, 0.1, 0.05])
        self.reset_button = Button(ax_reset, 'Reset', color='lightcoral', hovercolor='red')
        
        ax_pause = plt.axes([0.36, 0.05, 0.1, 0.05])
        self.pause_button = Button(ax_pause, 'Pause', color='lightyellow', hovercolor='yellow')
        
        ax_export = plt.axes([0.47, 0.05, 0.1, 0.05])
        self.export_button = Button(ax_export, 'Export', color='lightgreen', hovercolor='green')
        
        # Wave type selection
        ax_wave_type = plt.axes([0.02, 0.15, 0.18, 0.2], facecolor=slider_color)
        self.wave_type_radio = RadioButtons(ax_wave_type, 
                                           ('sine', 'cosine', 'square', 'sawtooth', 'triangle'), 
                                           active=0)
        
        # Color selection
        ax_color = plt.axes([0.02, 0.40, 0.18, 0.2], facecolor=slider_color)
        self.color_radio = RadioButtons(ax_color, 
                                       ('blue', 'red', 'green', 'purple', 'orange', 'cyan'), 
                                       active=0)
        
        # Display options
        ax_check = plt.axes([0.02, 0.65, 0.18, 0.15], facecolor=slider_color)
        self.check_buttons = CheckButtons(ax_check, 
                                         ['Show Points', 'Show Grid', 'Show Equation'], 
                                         [True, True, True])
    
    def setup_callbacks(self):
        """Setup all callback functions"""
        self.freq_slider.on_changed(self.update_frequency)
        self.amp_slider.on_changed(self.update_amplitude)
        self.phase_slider.on_changed(self.update_phase)
        self.speed_slider.on_changed(self.update_speed)
        self.reset_button.on_clicked(self.reset_parameters)
        self.pause_button.on_clicked(self.toggle_pause)
        self.export_button.on_clicked(self.export_data)
        self.wave_type_radio.on_clicked(self.update_wave_type)
        self.color_radio.on_clicked(self.update_color)
        self.check_buttons.on_clicked(self.update_display_options)
        
        self.paused = False
    
    def calculate_wave(self, x, frame_phase, wave_type):
        """Calculate wave based on type and parameters"""
        phase = frame_phase + self.params['phase_offset']
        freq = self.params['frequency']
        amp = self.params['amplitude']
        
        if wave_type == 'sine':
            return amp * np.sin(freq * x - phase)
        elif wave_type == 'cosine':
            return amp * np.cos(freq * x - phase)
        elif wave_type == 'square':
            return amp * np.sign(np.sin(freq * x - phase))
        elif wave_type == 'sawtooth':
            t = (freq * x - phase) / (2 * np.pi)
            return amp * (2 * (t - np.floor(0.5 + t)))
        elif wave_type == 'triangle':
            t = (freq * x - phase) / (2 * np.pi)
            return amp * (2 * np.abs(2 * (t - np.floor(t + 0.5))) - 1)
    
    def get_equation_text(self):
        """Get equation text for current wave"""
        eq = f"y = {self.params['amplitude']:.1f} × "
        if self.params['wave_type'] == 'sine':
            eq += f"sin({self.params['frequency']:.1f}x - φ)"
        elif self.params['wave_type'] == 'cosine':
            eq += f"cos({self.params['frequency']:.1f}x - φ)"
        elif self.params['wave_type'] == 'square':
            eq += f"sgn(sin({self.params['frequency']:.1f}x - φ))"
        elif self.params['wave_type'] == 'sawtooth':
            eq += f"sawtooth({self.params['frequency']:.1f}x - φ)"
        elif self.params['wave_type'] == 'triangle':
            eq += f"triangle({self.params['frequency']:.1f}x - φ)"
        
        eq += f"\nφ = {self.params['phase_offset']:.2f} rad"
        eq += f"\nf = {self.params['frequency']:.1f} Hz"
        return eq
    
    def update_animation(self, frame):
        """Update animation frame"""
        if not self.paused:
            self.animation_phase = (self.animation_phase + self.params['animation_speed']) % (2*np.pi)
        
        # Calculate wave
        y = self.calculate_wave(self.x, self.animation_phase, self.params['wave_type'])
        self.line.set_data(self.x, y)
        
        # Update points if enabled
        if self.params['show_points']:
            point_angles = np.linspace(0, 2*np.pi, 16, endpoint=False)
            point_y = self.calculate_wave(point_angles, self.animation_phase, self.params['wave_type'])
            self.points.set_data(point_angles, point_y)
        else:
            self.points.set_data([], [])
        
        # Update equation
        if hasattr(self, 'show_equation') and self.show_equation:
            self.equation_text.set_text(self.get_equation_text())
        else:
            self.equation_text.set_text('')
        
        return self.line, self.points, self.equation_text
    
    def start_animation(self):
        """Start the animation"""
        self.ani = FuncAnimation(self.fig, self.update_animation, frames=range(1000), 
                                interval=50, blit=True, repeat=True)
    
    # Callback functions
    def update_frequency(self, val):
        self.params['frequency'] = val
    
    def update_amplitude(self, val):
        self.params['amplitude'] = val
        self.ax_main.set_ylim(-val*1.2, val*1.2)
    
    def update_phase(self, val):
        self.params['phase_offset'] = val
    
    def update_speed(self, val):
        self.params['animation_speed'] = val
    
    def update_wave_type(self, label):
        self.params['wave_type'] = label
    
    def update_color(self, label):
        self.params['wave_color'] = label
        self.line.set_color(label)
    
    def update_display_options(self, label):
        if label == 'Show Points':
            self.params['show_points'] = not self.params['show_points']
        elif label == 'Show Grid':
            self.params['show_grid'] = not self.params['show_grid']
            self.ax_main.grid(self.params['show_grid'], alpha=0.3)
        elif label == 'Show Equation':
            self.show_equation = not getattr(self, 'show_equation', True)
    
    def reset_parameters(self, event):
        """Reset all parameters to defaults"""
        self.freq_slider.reset()
        self.amp_slider.reset()
        self.phase_slider.reset()
        self.speed_slider.reset()
        self.params.update({
            'frequency': 1.0, 'amplitude': 1.0, 'phase_offset': 0.0,
            'animation_speed': 0.05, 'wave_type': 'sine', 'wave_color': 'blue',
            'show_points': True, 'show_grid': True
        })
        self.wave_type_radio.set_active(0)
        self.color_radio.set_active(0)
        self.line.set_color('blue')
        self.ax_main.set_ylim(-1.5, 1.5)
        self.ax_main.grid(True, alpha=0.3)
    
    def toggle_pause(self, event):
        """Toggle animation pause"""
        self.paused = not self.paused
        self.pause_button.label.set_text('Resume' if self.paused else 'Pause')
    
    def export_data(self, event):
        """Export current wave data"""
        try:
            y = self.calculate_wave(self.x, self.animation_phase, self.params['wave_type'])
            
            # Save data to file
            data = np.column_stack((self.x, y))
            filename = f"sine_wave_{self.params['wave_type']}_{self.params['frequency']}Hz.csv"
            np.savetxt(filename, data, delimiter=',', header='x,y', comments='')
            
            # Show success message
            self.ax_main.text(0.5, 0.5, f'Data exported to {filename}', 
                            transform=self.ax_main.transAxes, ha='center',
                            bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8),
                            fontsize=12)
            self.fig.canvas.draw()
            
        except Exception as e:
            print(f"Export failed: {e}")
    
    def run(self):
        """Run the application"""
        # Add footer information
        self.fig.text(0.5, 0.02, 'Professional Sine Wave Generator v2.0 | Close window to exit', 
                     ha='center', fontsize=10, style='italic')
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.92, bottom=0.08)
        plt.show()

def main():
    """Main application entry point"""
    print("Starting Professional Sine Wave Generator...")
    app = SineWaveExecutableApp()
    app.run()
    print("Application closed.")

if __name__ == "__main__":
    main()
