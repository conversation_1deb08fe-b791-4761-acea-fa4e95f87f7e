#!/usr/bin/env python3
"""
Simple Working Sine Wave Generator
This is a minimal version that should work reliably
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from matplotlib.widgets import Slider
import matplotlib
matplotlib.use('TkAgg')

class SimpleSineWave:
    def __init__(self):
        print("Initializing Simple Sine Wave Generator...")
        
        # Parameters
        self.frequency = 1.0
        self.amplitude = 1.0
        self.phase = 0.0
        
        # Create figure and axis
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.fig.suptitle('Simple Sine Wave Generator', fontsize=16)
        
        # Set up the plot
        self.ax.set_xlim(0, 2*np.pi)
        self.ax.set_ylim(-2, 2)
        self.ax.set_xlabel('x (radians)')
        self.ax.set_ylabel('Amplitude')
        self.ax.grid(True, alpha=0.3)
        
        # Create x data
        self.x = np.linspace(0, 2*np.pi, 1000)
        
        # Create line object
        self.line, = self.ax.plot([], [], 'b-', linewidth=2)
        
        # Create sliders
        self.create_sliders()
        
        # Animation phase
        self.animation_phase = 0
        
        print("✓ Initialization complete")
    
    def create_sliders(self):
        """Create control sliders"""
        # Make room for sliders
        plt.subplots_adjust(bottom=0.25)
        
        # Frequency slider
        ax_freq = plt.axes([0.2, 0.15, 0.6, 0.03])
        self.freq_slider = Slider(ax_freq, 'Frequency', 0.1, 5.0, valinit=1.0)
        self.freq_slider.on_changed(self.update_frequency)
        
        # Amplitude slider
        ax_amp = plt.axes([0.2, 0.10, 0.6, 0.03])
        self.amp_slider = Slider(ax_amp, 'Amplitude', 0.1, 2.0, valinit=1.0)
        self.amp_slider.on_changed(self.update_amplitude)
        
        # Phase slider
        ax_phase = plt.axes([0.2, 0.05, 0.6, 0.03])
        self.phase_slider = Slider(ax_phase, 'Phase', 0, 2*np.pi, valinit=0.0)
        self.phase_slider.on_changed(self.update_phase)
        
        print("✓ Sliders created")
    
    def update_frequency(self, val):
        """Update frequency parameter"""
        self.frequency = val
    
    def update_amplitude(self, val):
        """Update amplitude parameter"""
        self.amplitude = val
        self.ax.set_ylim(-val*1.1, val*1.1)
    
    def update_phase(self, val):
        """Update phase parameter"""
        self.phase = val
    
    def calculate_sine_wave(self):
        """Calculate the sine wave with current parameters"""
        return self.amplitude * np.sin(self.frequency * self.x + self.phase + self.animation_phase)
    
    def animate(self, frame):
        """Animation function"""
        # Update animation phase
        self.animation_phase += 0.05
        
        # Calculate new y values
        y = self.calculate_sine_wave()
        
        # Update the line
        self.line.set_data(self.x, y)
        
        # Update title with current parameters
        title = f'Sine Wave: f={self.frequency:.1f}Hz, A={self.amplitude:.1f}, φ={self.phase:.2f}'
        self.ax.set_title(title)
        
        return self.line,
    
    def start_animation(self):
        """Start the animation"""
        print("Starting animation...")
        self.ani = FuncAnimation(self.fig, self.animate, interval=50, blit=True, repeat=True)
        
        # Show the plot
        print("Showing plot window...")
        plt.show()
        print("Plot window closed")

def main():
    """Main function"""
    print("=" * 50)
    print("SIMPLE SINE WAVE GENERATOR")
    print("=" * 50)
    
    try:
        # Create and run the application
        app = SimpleSineWave()
        app.start_animation()
        
        print("✓ Application completed successfully")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
