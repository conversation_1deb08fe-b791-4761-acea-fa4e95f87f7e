using System;
using System.Windows.Forms;

namespace SineWaveDeviceControl
{
    public partial class DeviceControlUI : Form
    {
        public DeviceControlUI()
        {
            InitializeComponent();
            
            // Initialize device number combo boxes with values
            for (int i = 1; i <= 10; i++)
            {
                comboBoxDevice1.Items.Add(i.ToString());
                comboBoxDevice2.Items.Add(i.ToString());
                comboBoxDevice3.Items.Add(i.ToString());
                comboBoxDevice4.Items.Add(i.ToString());
            }
            
            // Set default selections
            comboBoxDevice1.SelectedIndex = 0;
            comboBoxDevice2.SelectedIndex = 0;
            comboBoxDevice3.SelectedIndex = 0;
            comboBoxDevice4.SelectedIndex = 0;
        }

        private void btnStart_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Start button clicked");
        }

        private void btnStop_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Stop button clicked");
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Reset button clicked");
        }

        private void btnSettings_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Settings button clicked");
        }
    }
}
