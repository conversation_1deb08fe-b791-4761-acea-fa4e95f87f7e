# 🌊 Sine Wave Generator GUI

A clean, simple, and reliable sine wave generator with interactive controls.

## Features

- **Interactive Controls**: Real-time adjustment of wave parameters
- **Multiple Wave Types**: Sine, Cosine, Square, and Sawtooth waves
- **Animation Controls**: Pause/resume and speed adjustment
- **Parameter Controls**: 
  - Frequency (0.1 - 5.0 Hz)
  - Amplitude (0.1 - 2.0)
  - Phase (0 - 2π radians)
  - Animation Speed (0.01 - 0.5)

## Requirements

- Python 3.6+
- NumPy
- Matplotlib

## Installation

1. Install Python dependencies:
```bash
pip install numpy matplotlib
```

## Usage

### Method 1: Direct Python
```bash
python sine_wave_gui.py
```

### Method 2: Using Launcher
```bash
python run_sine_wave.py
```

### Method 3: Batch File (Windows)
```bash
launch_sine_wave.bat
```

## Controls

- **Frequency Slider**: Adjusts wave frequency (Hz)
- **Amplitude Slider**: Adjusts wave amplitude
- **Phase Slider**: Adjusts phase shift (radians)
- **Speed Slider**: Controls animation speed
- **Wave Type Radio Buttons**: Select wave type (sine, cosine, square, sawtooth)
- **Pause Button**: Pause/resume animation
- **Reset Button**: Reset all parameters to defaults

## Files

- `sine_wave_gui.py` - Main GUI application
- `run_sine_wave.py` - Launcher with dependency checking
- `launch_sine_wave.bat` - Windows batch launcher
- `SINE_WAVE_GUI_README.md` - This documentation

## Troubleshooting

If the GUI window doesn't appear:
1. Check that all dependencies are installed
2. Try running from command line to see error messages
3. Ensure Python and matplotlib are properly configured
4. Check that no firewall is blocking the application

## Mathematical Formula

The generated waves follow these formulas:

- **Sine**: `A × sin(f × t + φ)`
- **Cosine**: `A × cos(f × t + φ)`
- **Square**: `A × sign(sin(f × t + φ))`
- **Sawtooth**: `A × sawtooth(f × t + φ)`

Where:
- A = Amplitude
- f = Frequency
- t = Time
- φ = Phase
