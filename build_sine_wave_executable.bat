@echo off
echo Building Professional Sine Wave Generator Executable...
echo This may take several minutes...
echo.

set PYTHONPATH=%PYTHONPATH%;%CD%

echo Cleaning previous builds...
if exist "build" rmdir /s /q "build"
if exist "dist\ProfessionalSineWaveGenerator.exe" del "dist\ProfessionalSineWaveGenerator.exe"

echo.
echo Building executable with PyInstaller...
"C:\Program Files\Python312\python.exe" -m PyInstaller ^
    --name="ProfessionalSineWaveGenerator" ^
    --windowed ^
    --onefile ^
    --icon=NONE ^
    --add-data="sine_wave_executable.py;." ^
    --hidden-import=matplotlib.backends.backend_tkagg ^
    --hidden-import=numpy ^
    --hidden-import=scipy ^
    sine_wave_executable.py

echo.
if exist "dist\ProfessionalSineWaveGenerator.exe" (
    echo ✓ Build successful!
    echo Executable created: dist\ProfessionalSineWaveGenerator.exe
    echo File size: 
    dir "dist\ProfessionalSineWaveGenerator.exe" | find "ProfessionalSineWaveGenerator.exe"
    echo.
    echo You can now run the executable by double-clicking:
    echo dist\ProfessionalSineWaveGenerator.exe
) else (
    echo ✗ Build failed!
    echo Check the output above for errors.
)

echo.
pause
