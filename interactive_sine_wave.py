import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider, <PERSON><PERSON>

def create_interactive_sine_wave():
    """
    Create an interactive sine wave visualization with sliders to adjust parameters.
    """
    # Initial parameters
    init_frequency = 2
    init_amplitude = 1
    init_phase = 0
    
    # Create the figure and the line that we will manipulate
    fig, ax = plt.subplots(figsize=(10, 8))
    plt.subplots_adjust(left=0.1, bottom=0.35)
    
    # Create time data
    t = np.linspace(0, 2, 1000)
    
    # Calculate initial sine wave
    initial_sine = init_amplitude * np.sin(2 * np.pi * init_frequency * t + init_phase)
    
    # Create the line plot
    line, = ax.plot(t, initial_sine, lw=2)
    
    # Add a horizontal line at y=0
    ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    
    # Set up the plot
    ax.set_xlabel('Time (seconds)')
    ax.set_ylabel('Amplitude')
    ax.set_title('Interactive Sine Wave')
    ax.grid(True)
    
    # Adjust the main plot to make room for the sliders
    fig.subplots_adjust(bottom=0.25)
    
    # Make a horizontal slider to control the frequency
    ax_freq = plt.axes([0.25, 0.15, 0.65, 0.03])
    freq_slider = Slider(
        ax=ax_freq,
        label='Frequency (Hz)',
        valmin=0.1,
        valmax=10,
        valinit=init_frequency,
    )
    
    # Make a horizontal slider to control the amplitude
    ax_amp = plt.axes([0.25, 0.1, 0.65, 0.03])
    amp_slider = Slider(
        ax=ax_amp,
        label='Amplitude',
        valmin=0.1,
        valmax=2,
        valinit=init_amplitude,
    )
    
    # Make a horizontal slider to control the phase
    ax_phase = plt.axes([0.25, 0.05, 0.65, 0.03])
    phase_slider = Slider(
        ax=ax_phase,
        label='Phase (radians)',
        valmin=0,
        valmax=2*np.pi,
        valinit=init_phase,
    )
    
    # Create a button to reset the sliders
    reset_ax = plt.axes([0.8, 0.025, 0.1, 0.04])
    reset_button = Button(reset_ax, 'Reset', hovercolor='0.975')
    
    # The function to be called anytime a slider's value changes
    def update(val):
        # Get current slider values
        freq = freq_slider.val
        amp = amp_slider.val
        phase = phase_slider.val
        
        # Update the line data
        line.set_ydata(amp * np.sin(2 * np.pi * freq * t + phase))
        
        # Update y-axis limits based on amplitude
        ax.set_ylim(-amp*1.1, amp*1.1)
        
        # Add wavelength and period information
        period = 1.0 / freq
        wavelength = 1.0 / freq  # In our time units
        
        # Clear previous text annotations
        for txt in ax.texts:
            txt.remove()
        
        # Add new text annotations
        ax.text(0.02, 0.95, f'Frequency: {freq:.2f} Hz\nPeriod: {period:.2f} s\nWavelength: {wavelength:.2f} s',
                transform=ax.transAxes, fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.5))
        
        # Draw vertical lines to show wavelength
        ax.clear()
        ax.plot(t, amp * np.sin(2 * np.pi * freq * t + phase), lw=2)
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        
        # Draw wavelength markers if frequency is not too high
        if freq <= 5:
            for i in range(int(2 * freq) + 1):
                if i / freq < 2:  # Only draw within our time range
                    ax.axvline(x=i / freq, color='r', linestyle='--', alpha=0.3)
                    if i > 0:  # Don't label the y-axis
                        ax.text(i / freq, amp*1.1, f'λ{i}', ha='center')
        
        ax.set_xlabel('Time (seconds)')
        ax.set_ylabel('Amplitude')
        ax.set_title('Interactive Sine Wave')
        ax.grid(True)
        ax.set_ylim(-amp*1.1, amp*1.1)
        ax.set_xlim(0, 2)
        
        # Add text annotations again (after clearing)
        ax.text(0.02, 0.95, f'Frequency: {freq:.2f} Hz\nPeriod: {period:.2f} s\nWavelength: {wavelength:.2f} s',
                transform=ax.transAxes, fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.5))
        
        fig.canvas.draw_idle()
    
    # Register the update function with each slider
    freq_slider.on_changed(update)
    amp_slider.on_changed(update)
    phase_slider.on_changed(update)
    
    # The function to reset the sliders to initial values
    def reset(event):
        freq_slider.reset()
        amp_slider.reset()
        phase_slider.reset()
    
    # Register the reset function with the reset button
    reset_button.on_clicked(reset)
    
    # Call update once to set up the plot with initial values
    update(None)
    
    plt.show()

if __name__ == "__main__":
    create_interactive_sine_wave()
