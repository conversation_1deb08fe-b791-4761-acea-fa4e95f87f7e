#!/usr/bin/env python3
"""
Window Detection Script
This script helps locate matplotlib/tkinter windows
"""

import sys
import os

def find_matplotlib_windows():
    """Try to find and bring matplotlib windows to front"""
    print("Searching for matplotlib windows...")
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib
        
        print(f"Matplotlib backend: {matplotlib.get_backend()}")
        
        # Get all figure managers
        managers = plt._pylab_helpers.Gcf.get_all_fig_managers()
        print(f"Found {len(managers)} matplotlib figure managers")
        
        if managers:
            for i, manager in enumerate(managers):
                print(f"Figure {i+1}: {manager}")
                try:
                    # Try to bring window to front
                    if hasattr(manager, 'window'):
                        if hasattr(manager.window, 'lift'):
                            manager.window.lift()
                        if hasattr(manager.window, 'focus_force'):
                            manager.window.focus_force()
                        if hasattr(manager.window, 'wm_attributes'):
                            manager.window.wm_attributes('-topmost', True)
                            manager.window.after(100, lambda: manager.window.wm_attributes('-topmost', False))
                    
                    # Force draw
                    manager.canvas.draw()
                    print(f"  ✓ Attempted to bring Figure {i+1} to front")
                    
                except Exception as e:
                    print(f"  ✗ Could not manipulate Figure {i+1}: {e}")
        else:
            print("No matplotlib figures found")
            
    except Exception as e:
        print(f"Error accessing matplotlib: {e}")

def check_tkinter():
    """Check if tkinter is working"""
    print("\nTesting tkinter...")
    try:
        import tkinter as tk
        
        # Create a simple test window
        root = tk.Tk()
        root.title("Window Detection Test")
        root.geometry("300x100")
        root.lift()
        root.attributes('-topmost', True)
        
        label = tk.Label(root, text="If you can see this, tkinter is working!\nClose this window to continue.", 
                        font=('Arial', 12))
        label.pack(expand=True)
        
        # Auto-close after 3 seconds
        root.after(3000, root.destroy)
        
        print("✓ Tkinter test window created")
        print("  Look for a small window titled 'Window Detection Test'")
        
        root.mainloop()
        print("✓ Tkinter test completed")
        
    except Exception as e:
        print(f"✗ Tkinter test failed: {e}")

def main():
    print("=" * 50)
    print("WINDOW DETECTION AND RECOVERY TOOL")
    print("=" * 50)
    
    find_matplotlib_windows()
    check_tkinter()
    
    print("\n" + "=" * 50)
    print("DETECTION COMPLETE")
    print("=" * 50)

if __name__ == "__main__":
    main()
