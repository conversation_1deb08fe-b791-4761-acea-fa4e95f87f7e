@echo off
echo 🔨 BUILDING SINE WAVE GENERATOR EXECUTABLE
echo ==========================================
echo.

echo Step 1: Installing PyInstaller (if needed)...
pip install pyinstaller
echo.

echo Step 2: Building executable...
pyinstaller --onefile --windowed --name "SineWaveGenerator" --icon=NONE sine_wave_gui.py
echo.

echo Step 3: Checking build results...
if exist "dist\SineWaveGenerator.exe" (
    echo ✅ SUCCESS! Executable created: dist\SineWaveGenerator.exe
    echo.
    echo File details:
    dir "dist\SineWaveGenerator.exe"
    echo.
    echo You can now run the executable by double-clicking:
    echo dist\SineWaveGenerator.exe
) else (
    echo ❌ ERROR: Executable was not created successfully.
    echo Check the build output above for errors.
)

echo.
echo Build process complete.
pause
