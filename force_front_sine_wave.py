#!/usr/bin/env python3
"""
FORCE FRONT SINE WAVE GENERATOR
This version forces the window to appear in front
"""

import numpy as np
import matplotlib
matplotlib.use('TkAgg', force=True)
import matplotlib.pyplot as plt
from matplotlib.animation import Fun<PERSON><PERSON>nimation
from matplotlib.widgets import Slider
import tkinter as tk

def main():
    print("LAUNCHING SINE WAVE GENERATOR...")
    print("This window will appear in front of all other windows.")
    
    # Create root window and force it to front
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    root.lift()
    root.attributes('-topmost', True)
    root.after_idle(root.attributes, '-topmost', False)
    
    # Create matplotlib figure
    fig, ax = plt.subplots(figsize=(10, 6))
    fig.suptitle('SINE WAVE GENERATOR - FORCED TO FRONT', fontsize=14, fontweight='bold')
    
    # Get the matplotlib window and force it to front
    manager = fig.canvas.manager
    if hasattr(manager, 'window'):
        if hasattr(manager.window, 'wm_attributes'):
            manager.window.wm_attributes('-topmost', 1)
            manager.window.wm_attributes('-topmost', 0)
        if hasattr(manager.window, 'lift'):
            manager.window.lift()
        if hasattr(manager.window, 'focus_force'):
            manager.window.focus_force()
    
    # Set up plot
    ax.set_xlim(0, 2*np.pi)
    ax.set_ylim(-2, 2)
    ax.set_xlabel('x (radians)')
    ax.set_ylabel('sin(x)')
    ax.grid(True)
    
    # Create data
    x = np.linspace(0, 2*np.pi, 1000)
    line, = ax.plot(x, np.sin(x), 'b-', linewidth=2)
    
    # Create slider
    plt.subplots_adjust(bottom=0.25)
    ax_freq = plt.axes([0.2, 0.1, 0.6, 0.03])
    freq_slider = Slider(ax_freq, 'Frequency', 0.1, 5.0, valinit=1.0)
    
    def update(val):
        freq = freq_slider.val
        y = np.sin(freq * x)
        line.set_ydata(y)
        ax.set_title(f'Sine Wave - Frequency: {freq:.1f} Hz')
        fig.canvas.draw()
    
    freq_slider.on_changed(update)
    
    # Initial update
    update(1.0)
    
    # Add prominent text
    fig.text(0.5, 0.02, '🎉 SUCCESS! If you can see this window, the sine wave generator is working! 🎉', 
             ha='center', fontsize=12, fontweight='bold', color='green')
    
    print("✓ Window should now be visible!")
    print("✓ If you can see a sine wave with a frequency slider, it's working!")
    
    plt.show()
    
    print("✓ Application completed successfully!")
    root.destroy()

if __name__ == "__main__":
    main()
