import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import Fun<PERSON><PERSON><PERSON><PERSON>

def create_persistent_sine_wave():
    """
    Create a sine wave visualization that stays open until manually closed.
    The sine wave will animate continuously.
    """
    # Create figure and axis
    fig, ax = plt.subplots(figsize=(10, 6))
    fig.suptitle('Sine Wave Visualization', fontsize=16)
    
    # Set up the axis
    ax.set_xlim(0, 2*np.pi)
    ax.set_ylim(-1.2, 1.2)
    ax.set_xlabel('Angle (radians)')
    ax.set_ylabel('Amplitude')
    ax.grid(True)
    
    # Add a horizontal line at y=0
    ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    
    # Create a line object for the sine wave
    line, = ax.plot([], [], 'b-', linewidth=2)
    
    # Create a scatter object for points on the sine wave
    points, = ax.plot([], [], 'ro', markersize=8)
    
    # Add text for displaying the current phase
    phase_text = ax.text(0.02, 0.95, '', transform=ax.transAxes, 
                         fontsize=10, verticalalignment='top',
                         bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Create x data (fixed)
    x = np.linspace(0, 2*np.pi, 1000)
    
    # Function to initialize the animation
    def init():
        line.set_data([], [])
        points.set_data([], [])
        phase_text.set_text('')
        return line, points, phase_text
    
    # Function to update the animation for each frame
    def update(frame):
        # Calculate phase offset
        phase = frame * 0.05 % (2*np.pi)
        
        # Calculate the sine wave with the current phase
        y = np.sin(x - phase)
        
        # Update the line
        line.set_data(x, y)
        
        # Create points at specific angles
        point_angles = np.linspace(0, 2*np.pi, 8, endpoint=False)
        point_x = point_angles
        point_y = np.sin(point_angles - phase)
        
        # Update the points
        points.set_data(point_x, point_y)
        
        # Update the phase text
        phase_text.set_text(f'Phase: {phase:.2f} radians')
        
        return line, points, phase_text
    
    # Create the animation
    ani = FuncAnimation(fig, update, frames=range(1000), 
                        init_func=init, blit=True, interval=50,
                        repeat=True)
    
    # Add a note about closing the window
    plt.figtext(0.5, 0.01, 'Close this window manually when done', 
                ha='center', fontsize=10, 
                bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.5))
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.1)
    
    # Show the plot (this will block until the window is closed)
    plt.show()

if __name__ == "__main__":
    print("Starting persistent sine wave visualization...")
    print("The window will stay open until you manually close it.")
    create_persistent_sine_wave()
    print("Visualization window closed.")
