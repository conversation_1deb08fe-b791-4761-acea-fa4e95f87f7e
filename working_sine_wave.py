#!/usr/bin/env python3
"""
WORKING SINE WAVE GENERATOR
This version is designed to work reliably with proper error handling
"""

import sys
import os

def main():
    print("=" * 60)
    print("WORKING SINE WAVE GENERATOR")
    print("=" * 60)
    
    # Step 1: Import required modules with error handling
    print("Step 1: Importing required modules...")
    
    try:
        import numpy as np
        print("✓ NumPy imported successfully")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        print("Please install NumPy: pip install numpy")
        input("Press Enter to exit...")
        return
    
    try:
        import matplotlib
        # Force TkAgg backend for better compatibility
        matplotlib.use('TkAgg', force=True)
        import matplotlib.pyplot as plt
        from matplotlib.animation import FuncAnimation
        from matplotlib.widgets import Slider, Button
        print("✓ Matplotlib imported successfully")
        print(f"  Backend: {matplotlib.get_backend()}")
    except ImportError as e:
        print(f"✗ Matplotlib import failed: {e}")
        print("Please install Matplotlib: pip install matplotlib")
        input("Press Enter to exit...")
        return
    except Exception as e:
        print(f"✗ Matplotlib configuration failed: {e}")
        print("Trying alternative backend...")
        try:
            matplotlib.use('Qt5Agg', force=True)
            import matplotlib.pyplot as plt
            from matplotlib.animation import FuncAnimation
            from matplotlib.widgets import Slider, Button
            print("✓ Matplotlib with Qt5Agg backend")
        except:
            print("✗ All backends failed. Using default...")
            import matplotlib.pyplot as plt
            from matplotlib.animation import FuncAnimation
            from matplotlib.widgets import Slider, Button
    
    # Step 2: Create the application
    print("\nStep 2: Creating sine wave application...")
    
    class WorkingSineWave:
        def __init__(self):
            # Parameters
            self.freq = 1.0
            self.amp = 1.0
            self.phase = 0.0
            self.anim_phase = 0.0
            
            # Create figure
            self.fig = plt.figure(figsize=(12, 8))
            self.fig.suptitle('Working Sine Wave Generator', fontsize=16, fontweight='bold')
            
            # Main plot
            self.ax = plt.axes([0.1, 0.3, 0.8, 0.6])
            self.ax.set_xlim(0, 2*np.pi)
            self.ax.set_ylim(-2, 2)
            self.ax.set_xlabel('x (radians)', fontsize=12)
            self.ax.set_ylabel('Amplitude', fontsize=12)
            self.ax.grid(True, alpha=0.3)
            
            # Create data
            self.x = np.linspace(0, 2*np.pi, 1000)
            self.line, = self.ax.plot([], [], 'b-', linewidth=3)
            
            # Create controls
            self.create_controls()
            
            print("✓ Application created successfully")
        
        def create_controls(self):
            # Frequency slider
            ax_freq = plt.axes([0.2, 0.20, 0.6, 0.03])
            self.freq_slider = Slider(ax_freq, 'Frequency (Hz)', 0.1, 5.0, valinit=1.0)
            self.freq_slider.on_changed(lambda val: setattr(self, 'freq', val))
            
            # Amplitude slider
            ax_amp = plt.axes([0.2, 0.15, 0.6, 0.03])
            self.amp_slider = Slider(ax_amp, 'Amplitude', 0.1, 2.0, valinit=1.0)
            self.amp_slider.on_changed(self.update_amplitude)
            
            # Phase slider
            ax_phase = plt.axes([0.2, 0.10, 0.6, 0.03])
            self.phase_slider = Slider(ax_phase, 'Phase (rad)', 0, 2*np.pi, valinit=0.0)
            self.phase_slider.on_changed(lambda val: setattr(self, 'phase', val))
            
            # Reset button
            ax_reset = plt.axes([0.4, 0.05, 0.2, 0.04])
            self.reset_btn = Button(ax_reset, 'Reset')
            self.reset_btn.on_clicked(self.reset)
            
            print("✓ Controls created successfully")
        
        def update_amplitude(self, val):
            self.amp = val
            self.ax.set_ylim(-val*1.1, val*1.1)
        
        def reset(self, event):
            self.freq_slider.reset()
            self.amp_slider.reset()
            self.phase_slider.reset()
            self.freq = 1.0
            self.amp = 1.0
            self.phase = 0.0
            self.ax.set_ylim(-2, 2)
        
        def animate(self, frame):
            # Update animation phase
            self.anim_phase += 0.05
            
            # Calculate sine wave
            y = self.amp * np.sin(self.freq * self.x + self.phase + self.anim_phase)
            
            # Update line
            self.line.set_data(self.x, y)
            
            # Update title
            title = f'f={self.freq:.1f}Hz, A={self.amp:.1f}, φ={self.phase:.2f}rad'
            self.ax.set_title(title, fontsize=12)
            
            return self.line,
        
        def run(self):
            print("✓ Starting animation...")
            
            # Create animation
            self.ani = FuncAnimation(self.fig, self.animate, interval=50, blit=True)
            
            # Add instructions
            self.fig.text(0.5, 0.02, 'Use sliders to adjust parameters • Click Reset to restore defaults • Close window to exit', 
                         ha='center', fontsize=10, style='italic')
            
            # Show the plot
            print("✓ Displaying application window...")
            print("\nINSTRUCTIONS:")
            print("- Use the sliders to adjust frequency, amplitude, and phase")
            print("- Click 'Reset' to restore default values")
            print("- Close the window to exit the application")
            print("\nIf you can see the window with sliders and an animated sine wave,")
            print("the application is working correctly!")
            
            plt.tight_layout()
            plt.show()
            
            print("✓ Application window closed")
    
    # Step 3: Run the application
    print("\nStep 3: Running the application...")
    
    try:
        app = WorkingSineWave()
        app.run()
        print("\n✓ Application completed successfully!")
        
    except Exception as e:
        print(f"\n✗ Application failed: {e}")
        import traceback
        traceback.print_exc()
        input("\nPress Enter to exit...")
        return
    
    print("\n" + "=" * 60)
    print("APPLICATION COMPLETED SUCCESSFULLY!")
    print("=" * 60)

if __name__ == "__main__":
    main()
