#!/usr/bin/env python3
"""
Simple launcher for the Sine Wave Generator
"""

import sys
import os

def check_dependencies():
    """Check if required packages are installed"""
    print("🔍 Checking dependencies...")
    
    try:
        import numpy as np
        print(f"✅ NumPy {np.__version__}")
    except ImportError:
        print("❌ NumPy not found. Install with: pip install numpy")
        return False
    
    try:
        import matplotlib
        print(f"✅ Matplotlib {matplotlib.__version__}")
    except ImportError:
        print("❌ Matplotlib not found. Install with: pip install matplotlib")
        return False
    
    return True

def main():
    """Main launcher function"""
    print("🌊 SINE WAVE GENERATOR LAUNCHER")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Missing dependencies. Please install required packages.")
        input("Press Enter to exit...")
        return
    
    print("\n🚀 Starting Sine Wave Generator...")
    
    try:
        # Import and run the GUI
        from sine_wave_gui import SineWaveGUI
        
        gui = SineWaveGUI()
        gui.show()
        
    except ImportError as e:
        print(f"❌ Could not import sine_wave_gui: {e}")
        print("Make sure sine_wave_gui.py is in the same directory.")
        input("Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ Error running GUI: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
