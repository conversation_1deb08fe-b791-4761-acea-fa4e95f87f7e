import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAni<PERSON>

def visualize_multiple_sine_waves():
    """
    Create a visualization showing multiple sine waves with different frequencies
    and their sum.
    """
    # Set up the figure and subplots
    fig, axs = plt.subplots(4, 1, figsize=(10, 10), sharex=True)
    fig.suptitle('Sine Wave Composition', fontsize=16)
    
    # Time data
    t = np.linspace(0, 1, 1000)
    
    # Create three sine waves with different frequencies
    freq1, freq2, freq3 = 1, 2, 4
    amp1, amp2, amp3 = 1.0, 0.5, 0.25
    
    wave1 = amp1 * np.sin(2 * np.pi * freq1 * t)
    wave2 = amp2 * np.sin(2 * np.pi * freq2 * t)
    wave3 = amp3 * np.sin(2 * np.pi * freq3 * t)
    
    # Sum of the waves
    composite_wave = wave1 + wave2 + wave3
    
    # Plot each wave
    axs[0].plot(t, wave1, 'r-')
    axs[0].set_title(f'Sine Wave 1: {freq1} Hz, Amplitude {amp1}')
    axs[0].set_ylabel('Amplitude')
    axs[0].grid(True)
    
    axs[1].plot(t, wave2, 'g-')
    axs[1].set_title(f'Sine Wave 2: {freq2} Hz, Amplitude {amp2}')
    axs[1].set_ylabel('Amplitude')
    axs[1].grid(True)
    
    axs[2].plot(t, wave3, 'b-')
    axs[2].set_title(f'Sine Wave 3: {freq3} Hz, Amplitude {amp3}')
    axs[2].set_ylabel('Amplitude')
    axs[2].grid(True)
    
    # Plot the composite wave
    axs[3].plot(t, composite_wave, 'k-')
    axs[3].set_title('Composite Wave (Sum of all three waves)')
    axs[3].set_xlabel('Time (seconds)')
    axs[3].set_ylabel('Amplitude')
    axs[3].grid(True)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    
    plt.show()

def animate_sine_wave():
    """
    Create an animation of a traveling sine wave.
    """
    # Set up the figure and axis
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.set_xlim(0, 2*np.pi)
    ax.set_ylim(-1.5, 1.5)
    ax.set_title('Traveling Sine Wave')
    ax.set_xlabel('Position (x)')
    ax.set_ylabel('Amplitude')
    ax.grid(True)
    
    # Create a line object
    line, = ax.plot([], [], 'b-', lw=2)
    
    # Create x data
    x = np.linspace(0, 2*np.pi, 1000)
    
    # Initialization function for the animation
    def init():
        line.set_data([], [])
        return line,
    
    # Animation function
    def animate(i):
        # Calculate the sine wave with a phase that changes with time
        y = np.sin(x - 0.1 * i)
        line.set_data(x, y)
        return line,
    
    # Create the animation
    anim = FuncAnimation(fig, animate, init_func=init,
                         frames=100, interval=50, blit=True)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("Choose a visualization to display:")
    print("1. Multiple Sine Waves and Their Sum")
    print("2. Animated Traveling Sine Wave")
    
    choice = input("Enter 1 or 2: ")
    
    if choice == "1":
        visualize_multiple_sine_waves()
    elif choice == "2":
        animate_sine_wave()
    else:
        print("Invalid choice. Please run again and enter 1 or 2.")
